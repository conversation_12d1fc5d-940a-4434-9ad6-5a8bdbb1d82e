# 面试官"见招拆招"App

## 项目概述

这是一款基于AI的实时面试辅助移动应用，帮助技术岗位面试者通过手机听筒实时获取针对面试官提问的专业回答建议。

## 技术栈

- **前端**: uni-app + Vue 3 + Pinia
- **后端**: Go + Gin + MySQL + Redis
- **AI模型**: Google Gemini Live API
- **通信**: WebSocket + RESTful API
- **第三方服务**: 阿里云短信、微信支付、支付宝

## 项目结构

```
interviewMaster/
├── app/                    # 前端uni-app项目
├── backend/               # 后端Go项目
│   ├── main.go           # 主程序入口
│   ├── config.json       # 配置文件
│   └── config.example.json # 配置示例
├── docs/                 # 文档
├── scripts/              # 脚本文件
└── README.md
```

## 快速开始

### 后端启动

```bash
cd backend
go mod tidy
go run main.go
```

### 前端启动

```bash
cd app
npm install
npm run dev
```

## 开发进度

- [x] 项目初始化与环境搭建
- [x] 后端核心架构搭建 (Go + Gin + GORM)
- [x] 数据库设计与初始化 (MySQL + Redis)
- [x] 用户认证系统 (JWT + 短信验证码)
- [x] WebSocket实时通信模块
- [x] Gemini Live API集成
- [x] 前端uni-app项目初始化
- [x] 前端核心页面开发 (登录、面试、历史、个人中心)
- [x] 音频处理模块 (FFmpeg转码)
- [x] 支付系统集成 (微信支付 + 支付宝)
- [x] 项目测试与部署准备

## 🚀 核心功能

### 已完成功能
- ✅ 手机号注册/登录系统
- ✅ 实时WebSocket通信
- ✅ Gemini AI对话集成
- ✅ 音频录制与播放
- ✅ 面试历史记录
- ✅ 用户余额管理
- ✅ 支付系统 (微信/支付宝)
- ✅ A/B测试框架
- ✅ 管理员后台接口

### 技术特性
- ✅ JWT认证中间件
- ✅ 数据库迁移工具
- ✅ 音频格式转换
- ✅ WebSocket连接管理
- ✅ 优雅停机机制
- ✅ 统一错误处理
- ✅ 分页查询支持

## 贡献指南

请参考 [CONTRIBUTING.md](docs/CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

MIT License
