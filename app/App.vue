<template>
  <view id="app">
    <!-- 应用根组件 -->
  </view>
</template>

<script setup>
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

// 应用启动
onLaunch(() => {
  console.log('App Launch')
  initApp()
})

// 应用显示
onShow(() => {
  console.log('App Show')
})

// 应用隐藏
onHide(() => {
  console.log('App Hide')
})

// 初始化应用
const initApp = () => {
  // 检查登录状态
  const token = uni.getStorageSync('token')
  if (token) {
    // 验证token有效性
    validateToken(token)
  }
}

// 验证token
const validateToken = (token) => {
  // TODO: 验证token
  console.log('验证token:', token)
}
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  border: none;
  font-size: 32rpx;
}

.btn-primary:active {
  background-color: #0056cc;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}
</style>
