{"name": "interview-master-app", "version": "1.0.0", "description": "面试官见招拆招App前端项目", "main": "main.js", "scripts": {"dev": "uni", "build": "uni build", "build:h5": "uni build:h5", "build:mp-weixin": "uni build:mp-weixin", "build:app": "uni build:app", "serve": "npm run dev"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-app-plus": "^3.0.0", "vue": "^3.3.0", "pinia": "^2.1.0", "uni-simple-router": "^2.0.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "@types/node": "^20.0.0", "sass": "^1.60.0", "typescript": "^5.0.0", "vite": "^4.3.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}