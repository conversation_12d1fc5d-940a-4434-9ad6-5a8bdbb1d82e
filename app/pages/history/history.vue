<template>
  <view class="history-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <picker 
        mode="date" 
        :value="filterDate" 
        @change="onDateChange"
        class="date-picker"
      >
        <view class="picker-text">
          {{ filterDate || '选择日期' }}
        </view>
      </picker>
      
      <button class="clear-filter" @click="clearFilter">
        清除筛选
      </button>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-number">{{ stats.total_count || 0 }}</text>
        <text class="stat-label">总次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.month_count || 0 }}</text>
        <text class="stat-label">本月</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.today_count || 0 }}</text>
        <text class="stat-label">今日</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ formatResponseTime(stats.avg_response_time) }}</text>
        <text class="stat-label">平均响应</text>
      </view>
    </view>
    
    <!-- 历史记录列表 -->
    <view class="history-list">
      <view 
        v-for="(item, index) in historyList" 
        :key="item.id"
        class="history-item"
        @click="showDetail(item)"
      >
        <view class="item-header">
          <text class="session-id">会话 {{ item.session_id.slice(-8) }}</text>
          <text class="time">{{ formatDateTime(item.created_at) }}</text>
        </view>
        
        <view class="question-section">
          <text class="label">问题:</text>
          <text class="content">{{ item.question_text || '语音问题' }}</text>
        </view>
        
        <view class="answer-section">
          <text class="label">回答:</text>
          <text class="content">{{ truncateText(item.answer_text, 100) }}</text>
        </view>
        
        <view class="item-footer">
          <view class="meta-info">
            <text class="response-time">响应: {{ item.response_time_ms }}ms</text>
            <text class="prompt-version">版本: {{ item.prompt_version }}</text>
          </view>
          
          <view class="feedback-buttons">
            <button 
              class="feedback-btn"
              :class="{ active: item.user_feedback === 1 }"
              @click.stop="giveFeedback(item.id, 1)"
            >
              👍
            </button>
            <button 
              class="feedback-btn"
              :class="{ active: item.user_feedback === -1 }"
              @click.stop="giveFeedback(item.id, -1)"
            >
              👎
            </button>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more" @click="loadMore">
        <text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="!loading && historyList.length === 0" class="empty-state">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无面试记录</text>
        <button class="start-btn" @click="startInterview">
          开始面试
        </button>
      </view>
    </view>
    
    <!-- 详情弹窗 -->
    <view v-if="showDetailModal" class="detail-modal" @click="closeDetail">
      <view class="detail-content" @click.stop>
        <view class="detail-header">
          <text class="detail-title">面试记录详情</text>
          <button class="close-btn" @click="closeDetail">×</button>
        </view>
        
        <view class="detail-body">
          <view class="detail-item">
            <text class="detail-label">会话ID:</text>
            <text class="detail-value">{{ selectedItem?.session_id }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">时间:</text>
            <text class="detail-value">{{ formatDateTime(selectedItem?.created_at) }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">问题:</text>
            <text class="detail-value">{{ selectedItem?.question_text || '语音问题' }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">回答:</text>
            <text class="detail-value">{{ selectedItem?.answer_text }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">响应时间:</text>
            <text class="detail-value">{{ selectedItem?.response_time_ms }}ms</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">提示词版本:</text>
            <text class="detail-value">{{ selectedItem?.prompt_version }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 数据状态
const historyList = ref([])
const stats = ref({})
const loading = ref(false)
const hasMore = ref(true)
const page = ref(1)
const pageSize = ref(20)

// 筛选状态
const filterDate = ref('')

// 详情弹窗
const showDetailModal = ref(false)
const selectedItem = ref(null)

// 加载历史记录
const loadHistory = async (reset = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    const params = {
      page: reset ? 1 : page.value,
      page_size: pageSize.value
    }
    
    if (filterDate.value) {
      params.date = filterDate.value
    }
    
    // TODO: 调用API获取历史记录
    // const response = await api.getInterviewHistory(params)
    
    // 模拟数据
    const mockData = {
      list: [
        {
          id: 1,
          session_id: 'session_1234567890',
          question_text: '请介绍一下JavaScript的闭包概念',
          answer_text: '闭包是JavaScript中的一个重要概念，它指的是函数能够访问其外部作用域中的变量，即使在函数外部调用时也是如此...',
          response_time_ms: 1200,
          user_feedback: 1,
          prompt_version: 'A',
          created_at: new Date().toISOString()
        }
      ],
      total: 1,
      page: 1,
      page_size: 20
    }
    
    if (reset) {
      historyList.value = mockData.list
      page.value = 1
    } else {
      historyList.value.push(...mockData.list)
    }
    
    hasMore.value = historyList.value.length < mockData.total
    page.value++
    
  } catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    // TODO: 调用API获取统计信息
    // const response = await api.getInterviewStats()
    
    // 模拟数据
    stats.value = {
      total_count: 25,
      month_count: 8,
      today_count: 2,
      avg_response_time: 1350
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loading.value) return
  loadHistory(false)
}

// 日期筛选
const onDateChange = (e) => {
  filterDate.value = e.detail.value
  loadHistory(true)
}

// 清除筛选
const clearFilter = () => {
  filterDate.value = ''
  loadHistory(true)
}

// 给出反馈
const giveFeedback = async (logId, feedback) => {
  try {
    // TODO: 调用API给出反馈
    // await api.giveFeedback(logId, feedback)
    
    const item = historyList.value.find(item => item.id === logId)
    if (item) {
      item.user_feedback = item.user_feedback === feedback ? 0 : feedback
    }
    
    uni.showToast({
      title: '反馈成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('反馈失败:', error)
    uni.showToast({
      title: '反馈失败',
      icon: 'none'
    })
  }
}

// 显示详情
const showDetail = (item) => {
  selectedItem.value = item
  showDetailModal.value = true
}

// 关闭详情
const closeDetail = () => {
  showDetailModal.value = false
  selectedItem.value = null
}

// 开始面试
const startInterview = () => {
  uni.navigateTo({
    url: '/pages/interview/interview'
  })
}

// 工具函数
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

const formatResponseTime = (ms) => {
  if (!ms) return '0ms'
  if (ms < 1000) return `${ms}ms`
  return `${(ms / 1000).toFixed(1)}s`
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

onMounted(() => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }
  
  loadHistory(true)
  loadStats()
})
</script>

<style lang="scss" scoped>
.history-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  
  .date-picker {
    .picker-text {
      color: #333;
      font-size: 28rpx;
    }
  }
  
  .clear-filter {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8rpx;
    padding: 10rpx 20rpx;
    font-size: 24rpx;
  }
}

.stats-card {
  display: flex;
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .stat-label {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.history-list {
  .history-item {
    background: white;
    border-radius: 15rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .session-id {
        font-size: 24rpx;
        color: #667eea;
        font-weight: bold;
      }
      
      .time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .question-section, .answer-section {
      margin-bottom: 15rpx;
      
      .label {
        font-size: 24rpx;
        color: #666;
        margin-right: 10rpx;
      }
      
      .content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
      }
    }
    
    .item-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .meta-info {
        display: flex;
        gap: 20rpx;
        
        .response-time, .prompt-version {
          font-size: 22rpx;
          color: #999;
        }
      }
      
      .feedback-buttons {
        display: flex;
        gap: 10rpx;
        
        .feedback-btn {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          border: none;
          background: #f0f0f0;
          font-size: 24rpx;
          
          &.active {
            background: #667eea;
            color: white;
          }
        }
      }
    }
  }
  
  .load-more {
    text-align: center;
    padding: 40rpx;
    
    .load-text {
      color: #667eea;
      font-size: 28rpx;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 100rpx 40rpx;
    
    .empty-icon {
      display: block;
      font-size: 120rpx;
      margin-bottom: 30rpx;
    }
    
    .empty-text {
      display: block;
      font-size: 28rpx;
      color: #999;
      margin-bottom: 40rpx;
    }
    
    .start-btn {
      background: #667eea;
      color: white;
      border: none;
      border-radius: 25rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
    }
  }
}

.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .detail-content {
    background: white;
    border-radius: 20rpx;
    margin: 0 40rpx;
    max-height: 80vh;
    overflow-y: auto;
    
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #eee;
      
      .detail-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .close-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        border: none;
        background: #f0f0f0;
        font-size: 36rpx;
        color: #666;
      }
    }
    
    .detail-body {
      padding: 30rpx;
      
      .detail-item {
        margin-bottom: 30rpx;
        
        .detail-label {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-bottom: 10rpx;
        }
        
        .detail-value {
          font-size: 28rpx;
          color: #333;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
