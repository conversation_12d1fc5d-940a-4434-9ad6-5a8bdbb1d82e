<template>
  <view class="container">
    <view class="header">
      <text class="title">面试助手</text>
      <text class="subtitle">AI助力，面试无忧</text>
    </view>
    
    <view class="stats-card">
      <view class="stat-item">
        <text class="stat-number">{{ userInfo.balance_count || 0 }}</text>
        <text class="stat-label">剩余次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ userInfo.free_trial_count || 3 }}</text>
        <text class="stat-label">免费试用</text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="start-btn" @click="startInterview">
        <text class="btn-text">开始面试</text>
      </button>
      
      <button class="buy-btn" @click="goBuy">
        <text class="btn-text">购买套餐</text>
      </button>
    </view>
    
    <view class="features">
      <view class="feature-item">
        <text class="feature-icon">🎯</text>
        <text class="feature-title">实时回答</text>
        <text class="feature-desc">AI实时生成专业回答</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🔊</text>
        <text class="feature-title">语音播报</text>
        <text class="feature-desc">听筒私密播放答案</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">📚</text>
        <text class="feature-title">领域定制</text>
        <text class="feature-desc">针对不同技术领域优化</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = ref({})

onMounted(() => {
  loadUserInfo()
})

const loadUserInfo = async () => {
  try {
    await userStore.getUserInfo()
    userInfo.value = userStore.userInfo
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

const startInterview = () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }
  
  // 检查剩余次数
  if (userInfo.value.balance_count <= 0 && userInfo.value.free_trial_count <= 0) {
    uni.showModal({
      title: '提示',
      content: '您的使用次数已用完，请购买套餐',
      success: (res) => {
        if (res.confirm) {
          goBuy()
        }
      }
    })
    return
  }
  
  uni.navigateTo({
    url: '/pages/interview/interview'
  })
}

const goBuy = () => {
  if (!userStore.isLoggedIn) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }
  
  uni.navigateTo({
    url: '/pages/payment/payment'
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx 30rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.stats-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
  display: flex;
  justify-content: space-around;
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .stat-label {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.action-buttons {
  margin-bottom: 80rpx;
  
  .start-btn, .buy-btn {
    width: 100%;
    height: 100rpx;
    border-radius: 50rpx;
    border: none;
    margin-bottom: 30rpx;
    
    .btn-text {
      font-size: 32rpx;
      font-weight: bold;
    }
  }
  
  .start-btn {
    background: #ff6b6b;
    color: white;
  }
  
  .buy-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
  }
}

.features {
  .feature-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .feature-icon {
      font-size: 40rpx;
      margin-bottom: 15rpx;
      display: block;
    }
    
    .feature-title {
      display: block;
      font-size: 28rpx;
      font-weight: bold;
      color: white;
      margin-bottom: 10rpx;
    }
    
    .feature-desc {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>
