<template>
  <view class="interview-container">
    <!-- 状态栏 -->
    <view class="status-bar">
      <view class="status-info">
        <text class="status-text">{{ statusText }}</text>
        <view class="status-indicator" :class="statusClass"></view>
      </view>
      <view class="session-info">
        <text class="session-text">会话: {{ sessionId || '未连接' }}</text>
      </view>
    </view>
    
    <!-- 主控制区域 -->
    <view class="control-area">
      <!-- 中心按钮 -->
      <view class="center-button-container">
        <button 
          class="center-button"
          :class="{ 
            recording: isRecording,
            disabled: !canRecord
          }"
          @touchstart="startRecording"
          @touchend="stopRecording"
          @touchcancel="stopRecording"
        >
          <view class="button-icon">
            <text class="icon">{{ buttonIcon }}</text>
          </view>
          <text class="button-text">{{ buttonText }}</text>
        </button>
        
        <!-- 录音动画 -->
        <view v-if="isRecording" class="recording-animation">
          <view class="wave wave1"></view>
          <view class="wave wave2"></view>
          <view class="wave wave3"></view>
        </view>
      </view>
      
      <!-- 控制按钮组 -->
      <view class="control-buttons">
        <button 
          v-if="interviewStatus === 'speaking'"
          class="control-btn interrupt-btn"
          @click="interruptAnswer"
        >
          <text class="btn-icon">⏸</text>
          <text class="btn-text">打断</text>
        </button>
        
        <button 
          class="control-btn end-btn"
          @click="endInterview"
        >
          <text class="btn-icon">⏹</text>
          <text class="btn-text">结束</text>
        </button>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="history-header" @click="toggleHistory">
        <text class="history-title">历史记录</text>
        <text class="history-toggle">{{ showHistory ? '收起' : '展开' }}</text>
      </view>
      
      <view v-if="showHistory" class="history-list">
        <view 
          v-for="(item, index) in recentHistory" 
          :key="index"
          class="history-item"
        >
          <view class="question">
            <text class="label">问题:</text>
            <text class="content">{{ item.question }}</text>
          </view>
          <view class="answer">
            <text class="label">回答:</text>
            <text class="content">{{ item.answer }}</text>
          </view>
          <view class="meta">
            <text class="time">{{ formatTime(item.time) }}</text>
            <view class="feedback">
              <button 
                class="feedback-btn"
                :class="{ active: item.feedback === 1 }"
                @click="giveFeedback(item.id, 1)"
              >
                👍
              </button>
              <button 
                class="feedback-btn"
                :class="{ active: item.feedback === -1 }"
                @click="giveFeedback(item.id, -1)"
              >
                👎
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 新手引导 -->
    <view v-if="showGuide" class="guide-overlay" @click="closeGuide">
      <view class="guide-content" @click.stop>
        <view class="guide-step" v-if="guideStep === 1">
          <text class="guide-title">欢迎使用面试助手</text>
          <text class="guide-text">按住中心按钮说话，AI会实时为您提供专业的回答建议</text>
          <button class="guide-btn" @click="nextGuide">下一步</button>
        </view>
        
        <view class="guide-step" v-if="guideStep === 2">
          <text class="guide-title">听筒播放</text>
          <text class="guide-text">答案会通过听筒播放，请将手机靠近耳朵以获得最佳体验</text>
          <button class="guide-btn" @click="nextGuide">下一步</button>
        </view>
        
        <view class="guide-step" v-if="guideStep === 3">
          <text class="guide-title">打断功能</text>
          <text class="guide-text">在AI回答时，您可以点击"打断"按钮来停止当前回答</text>
          <button class="guide-btn" @click="closeGuide">开始使用</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useWebSocketStore } from '@/stores/websocket'

const userStore = useUserStore()
const wsStore = useWebSocketStore()

// 状态数据
const isRecording = ref(false)
const sessionId = ref('')
const interviewStatus = ref('idle') // idle, connecting, listening, thinking, speaking
const showHistory = ref(false)
const showGuide = ref(false)
const guideStep = ref(1)
const recentHistory = ref([])

// 录音相关
const recorderManager = uni.getRecorderManager()
const innerAudioContext = uni.createInnerAudioContext()

// 计算属性
const statusText = computed(() => {
  const statusMap = {
    idle: '空闲',
    connecting: '连接中...',
    listening: '正在聆听...',
    thinking: 'AI思考中...',
    speaking: '正在播报...',
    error: '连接错误'
  }
  return statusMap[interviewStatus.value] || '未知状态'
})

const statusClass = computed(() => {
  return `status-${interviewStatus.value}`
})

const buttonIcon = computed(() => {
  if (isRecording.value) return '🎤'
  if (interviewStatus.value === 'thinking') return '🤔'
  if (interviewStatus.value === 'speaking') return '🔊'
  return '🎯'
})

const buttonText = computed(() => {
  if (isRecording.value) return '松开结束'
  if (interviewStatus.value === 'thinking') return 'AI思考中'
  if (interviewStatus.value === 'speaking') return '正在播报'
  return '按住说话'
})

const canRecord = computed(() => {
  return wsStore.isConnected && 
         (interviewStatus.value === 'idle' || interviewStatus.value === 'listening')
})

// 开始录音
const startRecording = () => {
  if (!canRecord.value) return
  
  // 检查录音权限
  uni.authorize({
    scope: 'scope.record',
    success() {
      isRecording.value = true
      interviewStatus.value = 'listening'
      
      recorderManager.start({
        duration: 60000, // 最长60秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 48000,
        format: 'wav'
      })
    },
    fail() {
      uni.showModal({
        title: '权限申请',
        content: '需要录音权限才能使用面试功能',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting()
          }
        }
      })
    }
  })
}

// 停止录音
const stopRecording = () => {
  if (!isRecording.value) return
  
  isRecording.value = false
  interviewStatus.value = 'thinking'
  recorderManager.stop()
}

// 打断回答
const interruptAnswer = () => {
  wsStore.sendInterrupt()
  innerAudioContext.stop()
  interviewStatus.value = 'idle'
}

// 结束面试
const endInterview = () => {
  uni.showModal({
    title: '确认',
    content: '确定要结束本次面试吗？',
    success: (res) => {
      if (res.confirm) {
        wsStore.disconnect()
        uni.navigateBack()
      }
    }
  })
}

// 切换历史记录显示
const toggleHistory = () => {
  showHistory.value = !showHistory.value
}

// 给出反馈
const giveFeedback = (logId, feedback) => {
  // TODO: 调用API给出反馈
  const item = recentHistory.value.find(item => item.id === logId)
  if (item) {
    item.feedback = item.feedback === feedback ? 0 : feedback
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 新手引导
const nextGuide = () => {
  if (guideStep.value < 3) {
    guideStep.value++
  } else {
    closeGuide()
  }
}

const closeGuide = () => {
  showGuide.value = false
  // 保存引导状态
  uni.setStorageSync('guide_shown', true)
}

// 初始化
onMounted(async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }
  
  // 检查是否显示新手引导
  const guideShown = uni.getStorageSync('guide_shown')
  if (!guideShown) {
    showGuide.value = true
  }
  
  // 连接WebSocket
  interviewStatus.value = 'connecting'
  wsStore.connect()
  
  // 监听WebSocket消息
  wsStore.$subscribe((mutation, state) => {
    if (mutation.storeId === 'websocket') {
      interviewStatus.value = state.interviewStatus
      sessionId.value = state.currentSessionId
    }
  })
  
  // 设置录音事件监听
  recorderManager.onStart(() => {
    console.log('录音开始')
  })
  
  recorderManager.onStop((res) => {
    console.log('录音结束', res)
    // 发送音频数据
    if (res.tempFilePath) {
      wsStore.sendAudio(res.tempFilePath)
    }
  })
  
  recorderManager.onError((err) => {
    console.error('录音错误', err)
    isRecording.value = false
    interviewStatus.value = 'idle'
    uni.showToast({
      title: '录音失败',
      icon: 'none'
    })
  })
  
  // 设置音频播放事件监听
  innerAudioContext.onPlay(() => {
    console.log('音频播放开始')
  })
  
  innerAudioContext.onEnded(() => {
    console.log('音频播放结束')
    interviewStatus.value = 'idle'
  })
  
  innerAudioContext.onError((err) => {
    console.error('音频播放错误', err)
    interviewStatus.value = 'idle'
  })
})

onUnmounted(() => {
  // 清理资源
  recorderManager.stop()
  innerAudioContext.destroy()
  wsStore.disconnect()
})
</script>

<style lang="scss" scoped>
.interview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 30rpx;
  position: relative;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 60rpx;
  
  .status-info {
    display: flex;
    align-items: center;
    gap: 15rpx;
    
    .status-text {
      color: white;
      font-size: 28rpx;
    }
    
    .status-indicator {
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      
      &.status-idle {
        background: #4CAF50;
      }
      
      &.status-connecting {
        background: #FF9800;
        animation: pulse 1s infinite;
      }
      
      &.status-listening {
        background: #2196F3;
        animation: pulse 0.5s infinite;
      }
      
      &.status-thinking {
        background: #9C27B0;
        animation: pulse 0.8s infinite;
      }
      
      &.status-speaking {
        background: #F44336;
        animation: pulse 0.3s infinite;
      }
      
      &.status-error {
        background: #F44336;
      }
    }
  }
  
  .session-info {
    .session-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24rpx;
    }
  }
}

.control-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}

.center-button-container {
  position: relative;
  margin-bottom: 60rpx;
  
  .center-button {
    width: 300rpx;
    height: 300rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    
    &.recording {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      transform: scale(1.1);
    }
    
    &.disabled {
      opacity: 0.5;
    }
    
    .button-icon {
      font-size: 80rpx;
      margin-bottom: 10rpx;
    }
    
    .button-text {
      color: white;
      font-size: 24rpx;
      font-weight: bold;
    }
  }
  
  .recording-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    
    .wave {
      position: absolute;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      animation: wave 2s infinite;
      
      &.wave1 {
        width: 320rpx;
        height: 320rpx;
        margin: -160rpx 0 0 -160rpx;
      }
      
      &.wave2 {
        width: 360rpx;
        height: 360rpx;
        margin: -180rpx 0 0 -180rpx;
        animation-delay: 0.3s;
      }
      
      &.wave3 {
        width: 400rpx;
        height: 400rpx;
        margin: -200rpx 0 0 -200rpx;
        animation-delay: 0.6s;
      }
    }
  }
}

.control-buttons {
  display: flex;
  gap: 40rpx;
  
  .control-btn {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .btn-icon {
      font-size: 40rpx;
      margin-bottom: 5rpx;
    }
    
    .btn-text {
      font-size: 20rpx;
      color: white;
    }
    
    &.interrupt-btn {
      background: #FF9800;
    }
    
    &.end-btn {
      background: #F44336;
    }
  }
}

.history-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  overflow: hidden;
  
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    
    .history-title {
      color: white;
      font-size: 28rpx;
      font-weight: bold;
    }
    
    .history-toggle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24rpx;
    }
  }
  
  .history-list {
    max-height: 400rpx;
    overflow-y: auto;
    
    .history-item {
      padding: 30rpx;
      border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
      
      .question, .answer {
        margin-bottom: 15rpx;
        
        .label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 24rpx;
          margin-right: 10rpx;
        }
        
        .content {
          color: white;
          font-size: 26rpx;
        }
      }
      
      .meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .time {
          color: rgba(255, 255, 255, 0.6);
          font-size: 22rpx;
        }
        
        .feedback {
          display: flex;
          gap: 10rpx;
          
          .feedback-btn {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            font-size: 24rpx;
            
            &.active {
              background: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }
  }
}

.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .guide-content {
    background: white;
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    margin: 0 60rpx;
    text-align: center;
    
    .guide-step {
      .guide-title {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 30rpx;
      }
      
      .guide-text {
        display: block;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 40rpx;
      }
      
      .guide-btn {
        width: 200rpx;
        height: 80rpx;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 10rpx;
        font-size: 28rpx;
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}
</style>
