<template>
  <view class="payment-container">
    <!-- 商品列表 -->
    <view class="products-section">
      <text class="section-title">选择套餐</text>
      
      <view class="products-list">
        <view 
          v-for="product in products" 
          :key="product.id"
          class="product-item"
          :class="{ selected: selectedProduct?.id === product.id }"
          @click="selectProduct(product)"
        >
          <view class="product-header">
            <text class="product-name">{{ product.name }}</text>
            <view class="product-badge" v-if="product.id === 'trial_10'">
              推荐
            </view>
          </view>
          
          <text class="product-desc">{{ product.description }}</text>
          
          <view class="product-info">
            <view class="product-details">
              <text v-if="product.count > 0" class="detail-item">
                {{ product.count }}次面试机会
              </text>
              <text v-if="product.duration > 0" class="detail-item">
                {{ formatDuration(product.duration) }}使用时长
              </text>
            </view>
            
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-amount">{{ product.price }}</text>
            </view>
          </view>
          
          <view class="select-indicator" v-if="selectedProduct?.id === product.id">
            ✓
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支付方式 -->
    <view class="payment-methods-section" v-if="selectedProduct">
      <text class="section-title">支付方式</text>
      
      <view class="payment-methods">
        <view 
          v-for="method in paymentMethods" 
          :key="method.type"
          class="payment-method"
          :class="{ 
            selected: selectedPayment === method.type,
            disabled: !method.enabled 
          }"
          @click="selectPayment(method.type)"
        >
          <view class="method-icon">
            <text v-if="method.type === 'wechat'">💚</text>
            <text v-if="method.type === 'alipay'">💙</text>
          </view>
          
          <view class="method-info">
            <text class="method-name">{{ method.name }}</text>
            <text class="method-desc">{{ method.description }}</text>
          </view>
          
          <view class="method-radio">
            <view 
              class="radio-circle"
              :class="{ checked: selectedPayment === method.type }"
            ></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 订单信息 -->
    <view class="order-info-section" v-if="selectedProduct">
      <text class="section-title">订单信息</text>
      
      <view class="order-details">
        <view class="order-item">
          <text class="order-label">商品名称:</text>
          <text class="order-value">{{ selectedProduct.name }}</text>
        </view>
        
        <view class="order-item">
          <text class="order-label">商品价格:</text>
          <text class="order-value">¥{{ selectedProduct.price }}</text>
        </view>
        
        <view class="order-item">
          <text class="order-label">优惠金额:</text>
          <text class="order-value">¥0.00</text>
        </view>
        
        <view class="order-item total">
          <text class="order-label">实付金额:</text>
          <text class="order-value price">¥{{ selectedProduct.price }}</text>
        </view>
      </view>
    </view>
    
    <!-- 支付按钮 -->
    <view class="pay-button-section" v-if="selectedProduct">
      <button 
        class="pay-button"
        :disabled="!selectedPayment || paying"
        @click="createPayment"
      >
        {{ paying ? '处理中...' : `立即支付 ¥${selectedProduct.price}` }}
      </button>
    </view>
    
    <!-- 支付二维码弹窗 -->
    <view v-if="showQRCode" class="qr-modal" @click="closeQRCode">
      <view class="qr-content" @click.stop>
        <view class="qr-header">
          <text class="qr-title">扫码支付</text>
          <button class="close-btn" @click="closeQRCode">×</button>
        </view>
        
        <view class="qr-body">
          <view class="qr-code">
            <!-- 这里应该显示二维码图片 -->
            <text class="qr-placeholder">二维码</text>
          </view>
          
          <text class="qr-tip">请使用{{ paymentMethodName }}扫描二维码完成支付</text>
          
          <view class="qr-amount">
            <text class="amount-label">支付金额:</text>
            <text class="amount-value">¥{{ selectedProduct?.price }}</text>
          </view>
        </view>
        
        <view class="qr-footer">
          <button class="check-btn" @click="checkPaymentStatus">
            检查支付状态
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 数据状态
const products = ref([])
const paymentMethods = ref([])
const selectedProduct = ref(null)
const selectedPayment = ref('')
const paying = ref(false)
const showQRCode = ref(false)
const currentOrder = ref(null)

// 计算属性
const paymentMethodName = computed(() => {
  const method = paymentMethods.value.find(m => m.type === selectedPayment.value)
  return method?.name || ''
})

// 加载商品列表
const loadProducts = async () => {
  try {
    // TODO: 调用API获取商品列表
    // const response = await api.getProducts()
    
    // 模拟数据
    products.value = [
      {
        id: 'trial_10',
        name: '新手体验包',
        description: '10次面试机会，适合初次体验',
        price: 9.90,
        count: 10,
        duration: 0,
        type: 1
      },
      {
        id: 'standard_50',
        name: '标准套餐',
        description: '50次面试机会，适合求职准备',
        price: 39.90,
        count: 50,
        duration: 0,
        type: 1
      },
      {
        id: 'premium_100',
        name: '高级套餐',
        description: '100次面试机会，适合长期使用',
        price: 69.90,
        count: 100,
        duration: 0,
        type: 1
      },
      {
        id: 'monthly_unlimited',
        name: '包月畅享',
        description: '一个月内无限次使用',
        price: 99.90,
        count: 0,
        duration: 2592000,
        type: 3
      }
    ]
  } catch (error) {
    console.error('加载商品列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 加载支付方式
const loadPaymentMethods = async () => {
  try {
    // TODO: 调用API获取支付方式
    // const response = await api.getPaymentMethods()
    
    // 模拟数据
    paymentMethods.value = [
      {
        type: 'wechat',
        name: '微信支付',
        icon: 'wechat',
        description: '使用微信扫码支付',
        enabled: true
      },
      {
        type: 'alipay',
        name: '支付宝',
        icon: 'alipay',
        description: '使用支付宝扫码支付',
        enabled: true
      }
    ]
  } catch (error) {
    console.error('加载支付方式失败:', error)
  }
}

// 选择商品
const selectProduct = (product) => {
  selectedProduct.value = product
  // 默认选择第一个支付方式
  if (paymentMethods.value.length > 0) {
    selectedPayment.value = paymentMethods.value[0].type
  }
}

// 选择支付方式
const selectPayment = (type) => {
  const method = paymentMethods.value.find(m => m.type === type)
  if (method && method.enabled) {
    selectedPayment.value = type
  }
}

// 创建支付
const createPayment = async () => {
  if (!selectedProduct.value || !selectedPayment.value) return
  
  paying.value = true
  
  try {
    // 1. 创建订单
    const orderResponse = await createOrder()
    if (!orderResponse.success) {
      throw new Error(orderResponse.message)
    }
    
    currentOrder.value = orderResponse.data
    
    // 2. 创建支付
    let paymentResponse
    if (selectedPayment.value === 'wechat') {
      paymentResponse = await createWeChatPayment(currentOrder.value.order_no)
    } else if (selectedPayment.value === 'alipay') {
      paymentResponse = await createAlipayPayment(currentOrder.value.order_no)
    }
    
    if (!paymentResponse.success) {
      throw new Error(paymentResponse.message)
    }
    
    // 3. 显示支付二维码
    showQRCode.value = true
    
    // 4. 开始轮询支付状态
    startPaymentPolling()
    
  } catch (error) {
    console.error('创建支付失败:', error)
    uni.showToast({
      title: error.message || '支付失败',
      icon: 'none'
    })
  } finally {
    paying.value = false
  }
}

// 创建订单
const createOrder = async () => {
  try {
    // TODO: 调用API创建订单
    // const response = await api.createOrder({
    //   product_id: selectedProduct.value.id
    // })
    
    // 模拟成功响应
    return {
      success: true,
      data: {
        order_no: 'IM' + Date.now(),
        product_id: selectedProduct.value.id,
        amount: selectedProduct.value.price,
        status: 0
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '创建订单失败'
    }
  }
}

// 创建微信支付
const createWeChatPayment = async (orderNo) => {
  try {
    // TODO: 调用API创建微信支付
    // const response = await api.createWeChatPayment({
    //   order_no: orderNo
    // })
    
    // 模拟成功响应
    return {
      success: true,
      data: {
        code_url: 'weixin://wxpay/bizpayurl?pr=mock_code'
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '创建微信支付失败'
    }
  }
}

// 创建支付宝支付
const createAlipayPayment = async (orderNo) => {
  try {
    // TODO: 调用API创建支付宝支付
    // const response = await api.createAlipayPayment({
    //   order_no: orderNo
    // })
    
    // 模拟成功响应
    return {
      success: true,
      data: {
        qr_code: 'https://qr.alipay.com/mock_code'
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '创建支付宝支付失败'
    }
  }
}

// 开始轮询支付状态
const startPaymentPolling = () => {
  const timer = setInterval(async () => {
    const status = await checkPaymentStatus()
    if (status === 'paid') {
      clearInterval(timer)
      handlePaymentSuccess()
    } else if (status === 'failed') {
      clearInterval(timer)
      handlePaymentFailed()
    }
  }, 3000) // 每3秒检查一次
  
  // 5分钟后停止轮询
  setTimeout(() => {
    clearInterval(timer)
  }, 300000)
}

// 检查支付状态
const checkPaymentStatus = async () => {
  if (!currentOrder.value) return 'pending'
  
  try {
    // TODO: 调用API检查支付状态
    // const response = await api.getOrderStatus(currentOrder.value.order_no)
    
    // 模拟随机支付成功
    if (Math.random() > 0.8) {
      return 'paid'
    }
    return 'pending'
  } catch (error) {
    console.error('检查支付状态失败:', error)
    return 'pending'
  }
}

// 支付成功处理
const handlePaymentSuccess = () => {
  closeQRCode()
  
  uni.showToast({
    title: '支付成功',
    icon: 'success'
  })
  
  // 更新用户余额
  userStore.getUserInfo()
  
  // 跳转到成功页面或返回
  setTimeout(() => {
    uni.navigateBack()
  }, 2000)
}

// 支付失败处理
const handlePaymentFailed = () => {
  closeQRCode()
  
  uni.showToast({
    title: '支付失败',
    icon: 'none'
  })
}

// 关闭二维码弹窗
const closeQRCode = () => {
  showQRCode.value = false
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (days > 0) {
    return `${days}天`
  }
  if (hours > 0) {
    return `${hours}小时`
  }
  return `${minutes}分钟`
}

onMounted(() => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }
  
  loadProducts()
  loadPaymentMethods()
})
</script>

<style lang="scss" scoped>
.payment-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.products-section {
  margin-bottom: 30rpx;
  
  .products-list {
    .product-item {
      background: white;
      border-radius: 15rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      position: relative;
      border: 2rpx solid transparent;
      
      &.selected {
        border-color: #667eea;
      }
      
      .product-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15rpx;
        
        .product-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
        
        .product-badge {
          background: #ff6b6b;
          color: white;
          font-size: 20rpx;
          padding: 5rpx 15rpx;
          border-radius: 15rpx;
        }
      }
      
      .product-desc {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 20rpx;
      }
      
      .product-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        
        .product-details {
          .detail-item {
            display: block;
            font-size: 24rpx;
            color: #667eea;
            margin-bottom: 5rpx;
          }
        }
        
        .product-price {
          display: flex;
          align-items: baseline;
          
          .price-symbol {
            font-size: 24rpx;
            color: #ff6b6b;
          }
          
          .price-amount {
            font-size: 48rpx;
            font-weight: bold;
            color: #ff6b6b;
          }
        }
      }
      
      .select-indicator {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 40rpx;
        height: 40rpx;
        background: #667eea;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
      }
    }
  }
}

.payment-methods-section {
  margin-bottom: 30rpx;
  
  .payment-methods {
    background: white;
    border-radius: 15rpx;
    overflow: hidden;
    
    .payment-method {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.selected {
        background: #f8f9ff;
      }
      
      &.disabled {
        opacity: 0.5;
      }
      
      .method-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
      }
      
      .method-info {
        flex: 1;
        
        .method-name {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 5rpx;
        }
        
        .method-desc {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .method-radio {
        .radio-circle {
          width: 40rpx;
          height: 40rpx;
          border: 2rpx solid #ddd;
          border-radius: 50%;
          position: relative;
          
          &.checked {
            border-color: #667eea;
            
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 20rpx;
              height: 20rpx;
              background: #667eea;
              border-radius: 50%;
            }
          }
        }
      }
    }
  }
}

.order-info-section {
  margin-bottom: 30rpx;
  
  .order-details {
    background: white;
    border-radius: 15rpx;
    padding: 30rpx;
    
    .order-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.total {
        border-top: 1rpx solid #f0f0f0;
        padding-top: 20rpx;
        margin-top: 20rpx;
        
        .order-label, .order-value {
          font-weight: bold;
        }
      }
      
      .order-label {
        font-size: 28rpx;
        color: #666;
      }
      
      .order-value {
        font-size: 28rpx;
        color: #333;
        
        &.price {
          color: #ff6b6b;
          font-size: 32rpx;
        }
      }
    }
  }
}

.pay-button-section {
  .pay-button {
    width: 100%;
    height: 100rpx;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 15rpx;
    font-size: 32rpx;
    font-weight: bold;
    
    &:disabled {
      background: #ccc;
    }
  }
}

.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .qr-content {
    background: white;
    border-radius: 20rpx;
    margin: 0 40rpx;
    width: 600rpx;
    
    .qr-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #eee;
      
      .qr-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .close-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        border: none;
        background: #f0f0f0;
        font-size: 36rpx;
        color: #666;
      }
    }
    
    .qr-body {
      padding: 40rpx;
      text-align: center;
      
      .qr-code {
        width: 300rpx;
        height: 300rpx;
        background: #f0f0f0;
        border-radius: 15rpx;
        margin: 0 auto 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .qr-placeholder {
          color: #999;
          font-size: 28rpx;
        }
      }
      
      .qr-tip {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 20rpx;
      }
      
      .qr-amount {
        .amount-label {
          font-size: 24rpx;
          color: #666;
          margin-right: 10rpx;
        }
        
        .amount-value {
          font-size: 36rpx;
          font-weight: bold;
          color: #ff6b6b;
        }
      }
    }
    
    .qr-footer {
      padding: 30rpx;
      border-top: 1rpx solid #eee;
      
      .check-btn {
        width: 100%;
        height: 80rpx;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 10rpx;
        font-size: 28rpx;
      }
    }
  }
}
</style>
