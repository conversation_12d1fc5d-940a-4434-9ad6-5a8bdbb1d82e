<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="avatar-section">
        <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" />
        <view class="user-info">
          <text class="nickname">{{ userInfo.nickname || '面试者' }}</text>
          <text class="phone">{{ userInfo.phone }}</text>
        </view>
      </view>
      
      <button class="edit-btn" @click="editProfile">
        编辑
      </button>
    </view>
    
    <!-- 余额信息 -->
    <view class="balance-card">
      <view class="balance-header">
        <text class="balance-title">我的余额</text>
        <button class="recharge-btn" @click="goRecharge">
          充值
        </button>
      </view>
      
      <view class="balance-info">
        <view class="balance-item">
          <text class="balance-number">{{ userInfo.balance_count || 0 }}</text>
          <text class="balance-label">剩余次数</text>
        </view>
        <view class="balance-item">
          <text class="balance-number">{{ userInfo.free_trial_count || 0 }}</text>
          <text class="balance-label">免费试用</text>
        </view>
        <view class="balance-item">
          <text class="balance-number">{{ formatDuration(userInfo.balance_duration) }}</text>
          <text class="balance-label">剩余时长</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goHistory">
        <view class="menu-icon">📊</view>
        <text class="menu-text">面试历史</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goOrders">
        <view class="menu-icon">📋</view>
        <text class="menu-text">我的订单</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goSettings">
        <view class="menu-icon">⚙️</view>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goHelp">
        <view class="menu-icon">❓</view>
        <text class="menu-text">帮助与反馈</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        退出登录
      </button>
    </view>
    
    <!-- 编辑资料弹窗 -->
    <view v-if="showEditModal" class="edit-modal" @click="closeEdit">
      <view class="edit-content" @click.stop>
        <view class="edit-header">
          <text class="edit-title">编辑资料</text>
          <button class="close-btn" @click="closeEdit">×</button>
        </view>
        
        <view class="edit-body">
          <view class="edit-item">
            <text class="edit-label">昵称</text>
            <input 
              class="edit-input"
              v-model="editForm.nickname"
              placeholder="请输入昵称"
              maxlength="20"
            />
          </view>
          
          <view class="edit-item">
            <text class="edit-label">头像</text>
            <view class="avatar-upload" @click="chooseAvatar">
              <image 
                class="upload-avatar" 
                :src="editForm.avatar || '/static/default-avatar.png'" 
              />
              <text class="upload-text">点击更换</text>
            </view>
          </view>
        </view>
        
        <view class="edit-footer">
          <button class="cancel-btn" @click="closeEdit">取消</button>
          <button class="save-btn" @click="saveProfile">保存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 编辑弹窗
const showEditModal = ref(false)
const editForm = ref({
  nickname: '',
  avatar: ''
})

// 编辑资料
const editProfile = () => {
  editForm.value = {
    nickname: userInfo.value.nickname || '',
    avatar: userInfo.value.avatar || ''
  }
  showEditModal.value = true
}

// 关闭编辑
const closeEdit = () => {
  showEditModal.value = false
}

// 选择头像
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      editForm.value.avatar = res.tempFilePaths[0]
    }
  })
}

// 保存资料
const saveProfile = async () => {
  try {
    uni.showLoading({
      title: '保存中...'
    })
    
    // TODO: 上传头像和保存资料
    // if (editForm.value.avatar && editForm.value.avatar.startsWith('temp://')) {
    //   const uploadResult = await uploadAvatar(editForm.value.avatar)
    //   editForm.value.avatar = uploadResult.url
    // }
    
    // const result = await userStore.updateUserInfo(editForm.value)
    
    // 模拟保存成功
    userStore.setUserInfo(editForm.value)
    
    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    closeEdit()
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  }
}

// 跳转页面
const goRecharge = () => {
  uni.navigateTo({
    url: '/pages/payment/payment'
  })
}

const goHistory = () => {
  uni.switchTab({
    url: '/pages/history/history'
  })
}

const goOrders = () => {
  uni.navigateTo({
    url: '/pages/orders/orders'
  })
}

const goSettings = () => {
  uni.navigateTo({
    url: '/pages/settings/settings'
  })
}

const goHelp = () => {
  uni.navigateTo({
    url: '/pages/help/help'
  })
}

const goAbout = () => {
  uni.navigateTo({
    url: '/pages/about/about'
  })
}

// 退出登录
const logout = () => {
  uni.showModal({
    title: '确认',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

onMounted(async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }
  
  // 获取最新用户信息
  try {
    await userStore.getUserInfo()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .avatar-section {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
    }
    
    .user-info {
      .nickname {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: white;
        margin-bottom: 10rpx;
      }
      
      .phone {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
  
  .edit-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    border-radius: 25rpx;
    padding: 15rpx 30rpx;
    font-size: 24rpx;
  }
}

.balance-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .balance-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .recharge-btn {
      background: #667eea;
      color: white;
      border: none;
      border-radius: 25rpx;
      padding: 15rpx 30rpx;
      font-size: 24rpx;
    }
  }
  
  .balance-info {
    display: flex;
    justify-content: space-around;
    
    .balance-item {
      text-align: center;
      
      .balance-number {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 10rpx;
      }
      
      .balance-label {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.menu-section {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .menu-icon {
      font-size: 40rpx;
      margin-right: 20rpx;
    }
    
    .menu-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    
    .menu-arrow {
      font-size: 24rpx;
      color: #ccc;
    }
  }
}

.logout-section {
  .logout-btn {
    width: 100%;
    background: white;
    color: #f44336;
    border: 2rpx solid #f44336;
    border-radius: 15rpx;
    padding: 30rpx;
    font-size: 28rpx;
  }
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .edit-content {
    background: white;
    border-radius: 20rpx;
    margin: 0 40rpx;
    width: 600rpx;
    
    .edit-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #eee;
      
      .edit-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .close-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        border: none;
        background: #f0f0f0;
        font-size: 36rpx;
        color: #666;
      }
    }
    
    .edit-body {
      padding: 30rpx;
      
      .edit-item {
        margin-bottom: 30rpx;
        
        .edit-label {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 15rpx;
        }
        
        .edit-input {
          width: 100%;
          height: 80rpx;
          border: 2rpx solid #e0e0e0;
          border-radius: 10rpx;
          padding: 0 20rpx;
          font-size: 28rpx;
          box-sizing: border-box;
        }
        
        .avatar-upload {
          display: flex;
          align-items: center;
          gap: 20rpx;
          
          .upload-avatar {
            width: 120rpx;
            height: 120rpx;
            border-radius: 50%;
          }
          
          .upload-text {
            color: #667eea;
            font-size: 24rpx;
          }
        }
      }
    }
    
    .edit-footer {
      display: flex;
      gap: 20rpx;
      padding: 30rpx;
      border-top: 1rpx solid #eee;
      
      .cancel-btn, .save-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 10rpx;
        font-size: 28rpx;
        border: none;
      }
      
      .cancel-btn {
        background: #f0f0f0;
        color: #666;
      }
      
      .save-btn {
        background: #667eea;
        color: white;
      }
    }
  }
}
</style>
