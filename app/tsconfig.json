{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/static/*": ["./src/static/*"], "@/utils/*": ["./src/utils/*"], "@/stores/*": ["./src/stores/*"]}, "lib": ["esnext", "dom", "dom.iterable"], "types": ["@dcloudio/types", "@types/wechat-miniprogram", "node"]}, "include": ["*.ts", "*.tsx", "*.vue", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts"], "exclude": ["node_modules", "dist", "build"]}