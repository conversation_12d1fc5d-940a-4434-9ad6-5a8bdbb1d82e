
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#F8F8F8","background":"#efeff4","navigationBar":{"backgroundColor":"#F8F8F8","titleText":"面试助手","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"面试助手","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.66","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#999999","selectedColor":"#667eea","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"/static/tabbar/home.png","selectedIconPath":"/static/tabbar/home-active.png"},{"pagePath":"pages/history/history","text":"历史","iconPath":"/static/tabbar/history.png","selectedIconPath":"/static/tabbar/history-active.png"},{"pagePath":"pages/profile/profile","text":"我的","iconPath":"/static/tabbar/profile.png","selectedIconPath":"/static/tabbar/profile-active.png"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"backgroundColor":"#667eea","titleText":"面试助手","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/login/login","meta":{"navigationBar":{"backgroundColor":"#667eea","titleText":"登录注册","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/interview/interview","meta":{"navigationBar":{"backgroundColor":"#1e3c72","titleText":"面试进行中","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/history/history","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"backgroundColor":"#667eea","titleText":"面试历史","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/profile/profile","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"backgroundColor":"#667eea","titleText":"个人中心","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/payment/payment","meta":{"navigationBar":{"backgroundColor":"#667eea","titleText":"购买套餐","type":"default","titleColor":"#ffffff"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  