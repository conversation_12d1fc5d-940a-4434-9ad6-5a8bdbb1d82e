{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__interview_master", "name": "面试助手", "version": {"name": "1.0.0", "code": "100"}, "description": "AI面试辅助应用", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Audio": {}, "Camera": {}, "Contacts": {}, "Fingerprint": {}, "iBeacon": {}, "LivePusher": {}, "Maps": {"coordType": "gcj02"}, "Messaging": {}, "OAuth": {}, "Payment": {}, "Push": {}, "Share": {}, "Speech": {}, "SQLite": {}, "Statistic": {}, "VideoPlayer": {}, "Webview": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_LOGS\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-feature android:name=\"android.hardware.camera.autofocus\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />", "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "<uses-feature android:name=\"android.hardware.camera\" />", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\" />", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\" />"]}, "apple": {}, "plugins": {"payment": {}, "oauth": {}, "share": {}, "push": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#F8F8F8"}, "arguments": "{\"name\":\"\",\"path\":\"\",\"query\":\"\"}", "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.66", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#999999", "selectedColor": "#667eea", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "50px", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "/static/tabbar/home.png", "selectedIconPath": "/static/tabbar/home-active.png"}, {"pagePath": "pages/history/history", "text": "历史", "iconPath": "/static/tabbar/history.png", "selectedIconPath": "/static/tabbar/history-active.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "/static/tabbar/profile.png", "selectedIconPath": "/static/tabbar/profile-active.png"}], "backgroundColor": "#ffffff", "selectedIndex": 0, "shown": true, "child": ["lauchwebview"], "selected": 0}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}}, "launch_path": "__uniappview.html"}