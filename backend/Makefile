# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=interview-master
BINARY_UNIX=$(<PERSON>INARY_NAME)_unix

# Build the project
build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./

# Build for Linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v ./

# Clean build files
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

# Run the application
run:
	$(GOBUILD) -o $(BINARY_NAME) -v ./
	./$(BINARY_NAME)

# Run with hot reload (requires air)
dev:
	air

# Test
test:
	$(GOTEST) -v ./...

# Test with coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

# Download dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Database migration
migrate:
	$(GOBUILD) -o migrate ./cmd/migrate
	./migrate -action=migrate

# Reset database (dangerous!)
reset-db:
	$(GOBUILD) -o migrate ./cmd/migrate
	./migrate -action=reset -force

# Drop all tables (dangerous!)
drop-db:
	$(GOBUILD) -o migrate ./cmd/migrate
	./migrate -action=drop -force

# Format code
fmt:
	$(GOCMD) fmt ./...

# Lint code (requires golangci-lint)
lint:
	golangci-lint run

# Generate documentation
docs:
	swag init

# Docker build
docker-build:
	docker build -t interview-master .

# Docker run
docker-run:
	docker run -p 8080:8080 interview-master

# Install development tools
install-tools:
	$(GOGET) -u github.com/cosmtrek/air
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint
	$(GOGET) -u github.com/swaggo/swag/cmd/swag

# Help
help:
	@echo "Available commands:"
	@echo "  build         - Build the application"
	@echo "  build-linux   - Build for Linux"
	@echo "  clean         - Clean build files"
	@echo "  run           - Build and run the application"
	@echo "  dev           - Run with hot reload"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  deps          - Download dependencies"
	@echo "  migrate       - Run database migration"
	@echo "  reset-db      - Reset database (dangerous!)"
	@echo "  drop-db       - Drop all tables (dangerous!)"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  docs          - Generate documentation"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  install-tools - Install development tools"
	@echo "  help          - Show this help"

.PHONY: build build-linux clean run dev test test-coverage deps migrate reset-db drop-db fmt lint docs docker-build docker-run install-tools help
