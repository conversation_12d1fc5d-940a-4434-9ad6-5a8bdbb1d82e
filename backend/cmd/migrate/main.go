package main

import (
	"flag"
	"log"
	"os"

	"interviewmaster/internal/config"
	"interviewmaster/internal/model"
	"interviewmaster/pkg/database"
)

func main() {
	// 定义命令行参数
	var (
		configPath = flag.String("config", "config.json", "配置文件路径")
		action     = flag.String("action", "migrate", "操作类型: migrate, reset, drop")
		force      = flag.Bool("force", false, "强制执行（用于危险操作）")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库连接
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 根据操作类型执行相应的操作
	switch *action {
	case "migrate":
		log.Println("执行数据库迁移...")
		if err := model.AutoMigrate(db); err != nil {
			log.Fatalf("数据库迁移失败: %v", err)
		}
		if err := model.InitDefaultData(db); err != nil {
			log.Fatalf("初始化默认数据失败: %v", err)
		}
		log.Println("数据库迁移完成")

	case "reset":
		if !*force {
			log.Println("重置数据库是危险操作，请使用 -force 参数确认")
			os.Exit(1)
		}
		log.Println("重置数据库...")
		if err := model.ResetDatabase(db); err != nil {
			log.Fatalf("重置数据库失败: %v", err)
		}
		log.Println("数据库重置完成")

	case "drop":
		if !*force {
			log.Println("删除所有表是危险操作，请使用 -force 参数确认")
			os.Exit(1)
		}
		log.Println("删除所有表...")
		if err := model.DropAllTables(db); err != nil {
			log.Fatalf("删除表失败: %v", err)
		}
		log.Println("所有表删除完成")

	default:
		log.Printf("未知操作类型: %s", *action)
		log.Println("支持的操作类型: migrate, reset, drop")
		os.Exit(1)
	}
}
