package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// Config 应用配置结构
type Config struct {
	Server    ServerConfig    `json:"server"`
	Database  DatabaseConfig  `json:"database"`
	JWT       JWTConfig       `json:"jwt"`
	Gemini    GeminiConfig    `json:"gemini"`
	AliyunSMS AliyunSMSConfig `json:"aliyun_sms"`
	Payment   PaymentConfig   `json:"payment"`
	WebSocket WebSocketConfig `json:"websocket"`
	Audio     AudioConfig     `json:"audio"`
	ABTest    ABTestConfig    `json:"ab_test"`
	Log       LogConfig       `json:"log"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `json:"host"`
	Port         string `json:"port"`
	Mode         string `json:"mode"`
	ReadTimeout  int64  `json:"read_timeout"`
	WriteTimeout int64  `json:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL MySQLConfig `json:"mysql"`
	Redis RedisConfig `json:"redis"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host            string        `json:"host"`
	Port            string        `json:"port"`
	Username        string        `json:"username"`
	Password        string        `json:"password"`
	Database        string        `json:"database"`
	Charset         string        `json:"charset"`
	ParseTime       bool          `json:"parse_time"`
	Loc             string        `json:"loc"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	MaxOpenConns    int           `json:"max_open_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `json:"host"`
	Port         string `json:"port"`
	Password     string `json:"password"`
	DB           int    `json:"db"`
	PoolSize     int    `json:"pool_size"`
	MinIdleConns int    `json:"min_idle_conns"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret      string        `json:"secret"`
	ExpireHours time.Duration `json:"expire_hours"`
}

// GeminiConfig Gemini API配置
type GeminiConfig struct {
	APIKey                string        `json:"api_key"`
	Model                 string        `json:"model"`
	BaseURL               string        `json:"base_url"`
	Timeout               time.Duration `json:"timeout"`
	MaxConcurrentSessions int           `json:"max_concurrent_sessions"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Aliyun AliyunSMSConfig `json:"aliyun"`
}

// AliyunSMSConfig 阿里云短信配置
type AliyunSMSConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	SignName        string `json:"sign_name"`
	TemplateCode    string `json:"template_code"`
	Region          string `json:"region"`
	Test            bool   `json:"test"`
	TestCode        string `json:"testCode"`
}

// PaymentConfig 支付配置
type PaymentConfig struct {
	WeChat WeChatPayConfig `json:"wechat"`
	Alipay AlipayConfig    `json:"alipay"`
}

// WeChatPayConfig 微信支付配置
type WeChatPayConfig struct {
	AppID     string `json:"app_id"`
	MchID     string `json:"mch_id"`
	APIKey    string `json:"api_key"`
	NotifyURL string `json:"notify_url"`
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID      string `json:"app_id"`
	PrivateKey string `json:"private_key"`
	PublicKey  string `json:"public_key"`
	NotifyURL  string `json:"notify_url"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	MaxConnections    int           `json:"max_connections"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`
	BufferSize        int           `json:"buffer_size"`
}

// AudioConfig 音频配置
type AudioConfig struct {
	MaxFileSize    int64    `json:"max_file_size"`
	AllowedFormats []string `json:"allowed_formats"`
	TargetFormat   string   `json:"target_format"`
	SampleRate     int      `json:"sample_rate"`
	Channels       int      `json:"channels"`
	BitDepth       int      `json:"bit_depth"`
}

// ABTestConfig A/B测试配置
type ABTestConfig struct {
	Enabled      bool     `json:"enabled"`
	DefaultGroup string   `json:"default_group"`
	Groups       []string `json:"groups"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level"`
	FilePath   string `json:"file_path"`
	MaxSize    int    `json:"max_size"`
	MaxBackups int    `json:"max_backups"`
	MaxAge     int    `json:"max_age"`
}

var GlobalConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果配置文件不存在，尝试加载示例配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		configPath = "config.example.json"
	}

	file, err := os.Open(configPath)
	if err != nil {
		return nil, fmt.Errorf("打开配置文件失败: %v", err)
	}
	defer file.Close()

	var config Config
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	setDefaults(&config)

	GlobalConfig = &config
	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults(config *Config) {
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == "" {
		config.Server.Port = "8080"
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 60
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 60
	}

	if config.JWT.ExpireHours == 0 {
		config.JWT.ExpireHours = 24 * time.Hour
	}

	if config.WebSocket.MaxConnections == 0 {
		config.WebSocket.MaxConnections = 1000
	}
	if config.WebSocket.HeartbeatInterval == 0 {
		config.WebSocket.HeartbeatInterval = 30 * time.Second
	}
	if config.WebSocket.ReadTimeout == 0 {
		config.WebSocket.ReadTimeout = 60 * time.Second
	}
	if config.WebSocket.WriteTimeout == 0 {
		config.WebSocket.WriteTimeout = 10 * time.Second
	}

	if config.Gemini.Timeout == 0 {
		config.Gemini.Timeout = 30 * time.Second
	}
	if config.Gemini.MaxConcurrentSessions == 0 {
		config.Gemini.MaxConcurrentSessions = 100
	}
}

// GetDSN 获取MySQL连接字符串
func (c *MySQLConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=%t&loc=%s",
		c.Username, c.Password, c.Host, c.Port, c.Database, c.Charset, c.ParseTime, c.Loc)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.Host, c.Port)
}
