package handler

import (
	"io"
	"net/http"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/middleware"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService *service.PaymentService
	orderService   *service.OrderService
	productService *service.ProductService
}

// NewPaymentHandler 创建支付处理器实例
func NewPaymentHandler() *PaymentHandler {
	return &PaymentHandler{
		paymentService: service.NewPaymentService(),
		orderService:   service.NewOrderService(),
		productService: service.NewProductService(),
	}
}

// GetProducts 获取商品列表
func (h *PaymentHandler) GetProducts(c *gin.Context) {
	products, err := h.productService.GetProducts()
	if err != nil {
		utils.InternalErrorResponse(c, "获取商品列表失败")
		return
	}

	utils.SuccessResponse(c, products)
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	ProductID string `json:"product_id" binding:"required"`
}

// CreateOrder 创建订单
func (h *PaymentHandler) CreateOrder(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 创建支付订单
	order, err := h.paymentService.CreatePaymentOrder(userID, req.ProductID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, order)
}

// WeChatPayRequest 微信支付请求
type WeChatPayRequest struct {
	OrderNo string `json:"order_no" binding:"required"`
}

// CreateWeChatPayment 创建微信支付
func (h *PaymentHandler) CreateWeChatPayment(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req WeChatPayRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 获取客户端IP
	clientIP := c.ClientIP()

	// 创建微信支付
	paymentResp, err := h.paymentService.CreateWeChatPayment(req.OrderNo, clientIP)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, paymentResp)
}

// AlipayPayRequest 支付宝支付请求
type AlipayPayRequest struct {
	OrderNo   string `json:"order_no" binding:"required"`
	ReturnURL string `json:"return_url"`
}

// CreateAlipayPayment 创建支付宝支付
func (h *PaymentHandler) CreateAlipayPayment(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req AlipayPayRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 创建支付宝支付
	paymentResp, err := h.paymentService.CreateAlipayPayment(req.OrderNo, req.ReturnURL)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, paymentResp)
}

// WeChatNotify 微信支付回调
func (h *PaymentHandler) WeChatNotify(c *gin.Context) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 处理微信支付回调
	if err := h.paymentService.HandleWeChatNotify(string(body)); err != nil {
		c.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 返回成功响应
	c.String(http.StatusOK, "SUCCESS")
}

// AlipayNotify 支付宝支付回调
func (h *PaymentHandler) AlipayNotify(c *gin.Context) {
	// 获取所有参数
	params := make(map[string]string)
	for k, v := range c.Request.Form {
		if len(v) > 0 {
			params[k] = v[0]
		}
	}

	// 处理支付宝支付回调
	if err := h.paymentService.HandleAlipayNotify(params); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	// 返回成功响应
	c.String(http.StatusOK, "success")
}

// GetOrderStatus 获取订单状态
func (h *PaymentHandler) GetOrderStatus(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	orderNo := c.Param("order_no")
	if orderNo == "" {
		utils.ValidationErrorResponse(c, "订单号不能为空")
		return
	}

	// 获取订单信息
	order, err := h.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		utils.InternalErrorResponse(c, "获取订单信息失败")
		return
	}
	if order == nil {
		utils.NotFoundResponse(c, "订单不存在")
		return
	}

	// 检查订单是否属于当前用户
	if order.UserID != userID {
		utils.ForbiddenResponse(c, "无权访问此订单")
		return
	}

	utils.SuccessResponse(c, gin.H{
		"order_no": order.OrderNo,
		"status":   order.Status,
		"amount":   order.Amount,
		"pay_type": order.PayType,
		"paid_at":  order.PaidAt,
	})
}

// GetUserOrders 获取用户订单列表
func (h *PaymentHandler) GetUserOrders(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, pageSize := utils.GetPageParams(pageStr, pageSizeStr)

	// 获取用户订单列表
	orders, total, err := h.orderService.GetUserOrders(userID, page, pageSize)
	if err != nil {
		utils.InternalErrorResponse(c, "获取订单列表失败")
		return
	}

	utils.PageSuccessResponse(c, orders, total, page, pageSize)
}

// GetPaymentMethods 获取支付方式
func (h *PaymentHandler) GetPaymentMethods(c *gin.Context) {
	methods := h.paymentService.GetPaymentMethods()
	utils.SuccessResponse(c, methods)
}

// CancelOrder 取消订单
func (h *PaymentHandler) CancelOrder(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	orderNo := c.Param("order_no")
	if orderNo == "" {
		utils.ValidationErrorResponse(c, "订单号不能为空")
		return
	}

	// 获取订单信息
	order, err := h.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		utils.InternalErrorResponse(c, "获取订单信息失败")
		return
	}
	if order == nil {
		utils.NotFoundResponse(c, "订单不存在")
		return
	}

	// 检查订单是否属于当前用户
	if order.UserID != userID {
		utils.ForbiddenResponse(c, "无权操作此订单")
		return
	}

	// 取消订单
	if err := h.orderService.CloseOrder(orderNo); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "订单已取消", nil)
}

// GetOrderDetail 获取订单详情
func (h *PaymentHandler) GetOrderDetail(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	orderNo := c.Param("order_no")
	if orderNo == "" {
		utils.ValidationErrorResponse(c, "订单号不能为空")
		return
	}

	// 获取订单信息
	order, err := h.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		utils.InternalErrorResponse(c, "获取订单信息失败")
		return
	}
	if order == nil {
		utils.NotFoundResponse(c, "订单不存在")
		return
	}

	// 检查订单是否属于当前用户
	if order.UserID != userID {
		utils.ForbiddenResponse(c, "无权访问此订单")
		return
	}

	utils.SuccessResponse(c, order)
}

// AdminGetOrders 管理员获取订单列表
func (h *PaymentHandler) AdminGetOrders(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, pageSize := utils.GetPageParams(pageStr, pageSizeStr)

	// TODO: 实现管理员订单查询
	// 这里暂时返回空列表
	utils.PageSuccessResponse(c, []interface{}{}, 0, page, pageSize)
}

// AdminUpdateOrderStatus 管理员更新订单状态
func (h *PaymentHandler) AdminUpdateOrderStatus(c *gin.Context) {
	orderNo := c.Param("order_no")
	if orderNo == "" {
		utils.ValidationErrorResponse(c, "订单号不能为空")
		return
	}

	var req struct {
		Status int `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// TODO: 实现管理员订单状态更新
	utils.SuccessResponseWithMessage(c, "订单状态更新成功", nil)
}
