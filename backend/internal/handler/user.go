package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/middleware"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService *service.UserService
}

// NewUserHandler 创建用户处理器实例
func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: service.NewUserService(),
	}
}

// GetUserInfo 获取用户信息
func (h *UserHandler) GetUserInfo(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		utils.InternalErrorResponse(c, "获取用户信息失败")
		return
	}
	if user == nil {
		utils.NotFoundResponse(c, "用户不存在")
		return
	}

	// 脱敏处理
	user.Phone = utils.MaskPhone(user.Phone)

	utils.SuccessResponse(c, user)
}

// UpdateUserInfoRequest 更新用户信息请求
type UpdateUserInfoRequest struct {
	Nickname string `json:"nickname"`
}

// UpdateUserInfo 更新用户信息
func (h *UserHandler) UpdateUserInfo(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req UpdateUserInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 获取用户信息
	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		utils.InternalErrorResponse(c, "获取用户信息失败")
		return
	}
	if user == nil {
		utils.NotFoundResponse(c, "用户不存在")
		return
	}

	// 更新用户信息
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}

	if err := h.userService.UpdateUser(user); err != nil {
		utils.InternalErrorResponse(c, "更新用户信息失败")
		return
	}

	// 脱敏处理
	user.Phone = utils.MaskPhone(user.Phone)

	utils.SuccessResponseWithMessage(c, "更新成功", user)
}

// GetUserBalance 获取用户余额
func (h *UserHandler) GetUserBalance(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		utils.InternalErrorResponse(c, "获取用户信息失败")
		return
	}
	if user == nil {
		utils.NotFoundResponse(c, "用户不存在")
		return
	}

	balance := gin.H{
		"balance_count":     user.BalanceCount,
		"balance_duration":  user.BalanceDuration,
		"free_trial_count":  user.FreeTrialCount,
		"balance_duration_formatted": utils.FormatDuration(user.BalanceDuration),
	}

	utils.SuccessResponse(c, balance)
}

// UpdateUserBalanceRequest 更新用户余额请求
type UpdateUserBalanceRequest struct {
	CountDelta    int32 `json:"count_delta"`
	DurationDelta int32 `json:"duration_delta"`
}

// UpdateUserBalance 更新用户余额（管理员接口）
func (h *UserHandler) UpdateUserBalance(c *gin.Context) {
	// 获取用户ID参数
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		utils.ValidationErrorResponse(c, "无效的用户ID")
		return
	}

	var req UpdateUserBalanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 更新用户余额
	if err := h.userService.UpdateUserBalance(userID, req.CountDelta, req.DurationDelta); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "余额更新成功", nil)
}

// ConsumeUserBalanceRequest 消费用户余额请求
type ConsumeUserBalanceRequest struct {
	Type string `json:"type" binding:"required,oneof=count duration"`
}

// ConsumeUserBalance 消费用户余额
func (h *UserHandler) ConsumeUserBalance(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req ConsumeUserBalanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 消费用户余额
	if err := h.userService.ConsumeUserBalance(userID, req.Type); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "余额消费成功", nil)
}

// GetUserList 获取用户列表（管理员接口）
func (h *UserHandler) GetUserList(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")
	
	page, pageSize := utils.GetPageParams(pageStr, pageSizeStr)

	// TODO: 实现用户列表查询
	// 这里暂时返回空列表
	utils.PageSuccessResponse(c, []interface{}{}, 0, page, pageSize)
}

// DeleteUser 删除用户（管理员接口）
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// 获取用户ID参数
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		utils.ValidationErrorResponse(c, "无效的用户ID")
		return
	}

	// TODO: 实现用户删除逻辑
	// 这里暂时返回成功
	_ = userID
	utils.SuccessResponseWithMessage(c, "用户删除成功", nil)
}
