package handler

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/utils"
	"interviewmaster/pkg/websocket"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	ctx context.Context
}

// NewWebSocketHandler 创建WebSocket处理器实例
func NewWebSocketHandler(ctx context.Context) *WebSocketHandler {
	return &WebSocketHandler{
		ctx: ctx,
	}
}

// HandleConnection 处理WebSocket连接
func (h *WebSocketHandler) HandleConnection(c *gin.Context) {
	websocket.HandleWebSocket(c)
}

// GetConnectionStats 获取连接统计信息
func (h *WebSocketHandler) GetConnectionStats(c *gin.Context) {
	stats := websocket.GetConnectionStats()
	utils.SuccessResponse(c, stats)
}

// SendMessageToUserRequest 发送消息给用户请求
type SendMessageToUserRequest struct {
	UserID  uint64      `json:"user_id" binding:"required"`
	Type    string      `json:"type" binding:"required"`
	Data    interface{} `json:"data"`
	Status  string      `json:"status"`
}

// SendMessageToUser 发送消息给特定用户（管理员接口）
func (h *WebSocketHandler) SendMessageToUser(c *gin.Context) {
	var req SendMessageToUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	msg := websocket.Message{
		Type:   req.Type,
		Data:   req.Data,
		Status: req.Status,
	}

	err := websocket.SendMessageToUser(req.UserID, msg)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "发送消息失败")
		return
	}

	utils.SuccessResponseWithMessage(c, "消息发送成功", nil)
}

// BroadcastMessageRequest 广播消息请求
type BroadcastMessageRequest struct {
	Type   string      `json:"type" binding:"required"`
	Data   interface{} `json:"data"`
	Status string      `json:"status"`
}

// BroadcastMessage 广播消息给所有用户（管理员接口）
func (h *WebSocketHandler) BroadcastMessage(c *gin.Context) {
	var req BroadcastMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	msg := websocket.Message{
		Type:   req.Type,
		Data:   req.Data,
		Status: req.Status,
	}

	websocket.BroadcastMessage(msg)
	utils.SuccessResponseWithMessage(c, "广播消息发送成功", nil)
}

// CheckUserOnlineRequest 检查用户在线状态请求
type CheckUserOnlineRequest struct {
	UserID uint64 `json:"user_id" binding:"required"`
}

// CheckUserOnline 检查用户是否在线
func (h *WebSocketHandler) CheckUserOnline(c *gin.Context) {
	var req CheckUserOnlineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	isOnline := websocket.IsUserOnline(req.UserID)
	utils.SuccessResponse(c, gin.H{
		"user_id":   req.UserID,
		"is_online": isOnline,
	})
}

// CloseUserConnectionRequest 关闭用户连接请求
type CloseUserConnectionRequest struct {
	UserID uint64 `json:"user_id" binding:"required"`
}

// CloseUserConnection 关闭用户连接（管理员接口）
func (h *WebSocketHandler) CloseUserConnection(c *gin.Context) {
	var req CloseUserConnectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	websocket.CloseUserConnection(req.UserID)
	utils.SuccessResponseWithMessage(c, "用户连接已关闭", nil)
}
