package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"interviewmaster/pkg/jwt"
	"interviewmaster/internal/utils"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			utils.ErrorResponse(c, http.StatusUnauthorized, "缺少认证头")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			utils.ErrorResponse(c, http.StatusUnauthorized, "认证头格式错误")
			c.Abort()
			return
		}

		// 解析token
		token := parts[1]
		claims, err := jwt.ParseToken(token)
		if err != nil {
			utils.ErrorResponse(c, http.StatusUnauthorized, "无效的token")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("phone", claims.Phone)
		c.Set("token", token)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件（不强制要求认证）
func OptionalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && parts[0] == "Bearer" {
				token := parts[1]
				if claims, err := jwt.ParseToken(token); err == nil {
					c.Set("user_id", claims.UserID)
					c.Set("phone", claims.Phone)
					c.Set("token", token)
				}
			}
		}
		c.Next()
	}
}

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			utils.ErrorResponse(c, http.StatusUnauthorized, "缺少认证头")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			utils.ErrorResponse(c, http.StatusUnauthorized, "认证头格式错误")
			c.Abort()
			return
		}

		// 解析token
		token := parts[1]
		claims, err := jwt.ParseToken(token)
		if err != nil {
			utils.ErrorResponse(c, http.StatusUnauthorized, "无效的token")
			c.Abort()
			return
		}

		// TODO: 验证是否为管理员用户
		// 这里可以添加额外的管理员权限检查逻辑

		// 将管理员信息存储到上下文
		c.Set("admin_id", claims.UserID)
		c.Set("admin_phone", claims.Phone)
		c.Set("token", token)

		c.Next()
	}
}

// GetUserID 从上下文获取用户ID
func GetUserID(c *gin.Context) uint64 {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint64); ok {
			return id
		}
	}
	return 0
}

// GetPhone 从上下文获取手机号
func GetPhone(c *gin.Context) string {
	if phone, exists := c.Get("phone"); exists {
		if p, ok := phone.(string); ok {
			return p
		}
	}
	return ""
}

// GetToken 从上下文获取token
func GetToken(c *gin.Context) string {
	if token, exists := c.Get("token"); exists {
		if t, ok := token.(string); ok {
			return t
		}
	}
	return ""
}
