package model

import (
	"log"

	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	log.Println("开始数据库迁移...")

	// 迁移所有表
	err := db.AutoMigrate(
		&User{},
		&Order{},
		&InterviewLog{},
		&SMSCode{},
		&Product{},
		&Admin{},
	)

	if err != nil {
		log.Printf("数据库迁移失败: %v", err)
		return err
	}

	log.Println("数据库迁移完成")
	return nil
}

// InitDefaultData 初始化默认数据
func InitDefaultData(db *gorm.DB) error {
	log.Println("开始初始化默认数据...")

	// 检查并创建默认商品
	if err := initDefaultProducts(db); err != nil {
		return err
	}

	// 检查并创建默认管理员
	if err := initDefaultAdmin(db); err != nil {
		return err
	}

	log.Println("默认数据初始化完成")
	return nil
}

// initDefaultProducts 初始化默认商品
func initDefaultProducts(db *gorm.DB) error {
	// 检查是否已有商品数据
	var count int64
	db.Model(&Product{}).Count(&count)
	if count > 0 {
		log.Println("商品数据已存在，跳过初始化")
		return nil
	}

	// 创建默认商品
	products := []Product{
		{
			ID:          "trial_10",
			Name:        "新手体验包",
			Description: "10次面试机会，适合初次体验",
			Price:       9.90,
			Count:       10,
			Duration:    0,
			Type:        ProductTypeCount,
			Status:      1,
			SortOrder:   1,
		},
		{
			ID:          "standard_50",
			Name:        "标准套餐",
			Description: "50次面试机会，适合求职准备",
			Price:       39.90,
			Count:       50,
			Duration:    0,
			Type:        ProductTypeCount,
			Status:      1,
			SortOrder:   2,
		},
		{
			ID:          "premium_100",
			Name:        "高级套餐",
			Description: "100次面试机会，适合长期使用",
			Price:       69.90,
			Count:       100,
			Duration:    0,
			Type:        ProductTypeCount,
			Status:      1,
			SortOrder:   3,
		},
		{
			ID:          "monthly_unlimited",
			Name:        "包月畅享",
			Description: "一个月内无限次使用",
			Price:       99.90,
			Count:       0,
			Duration:    2592000, // 30天
			Type:        ProductTypeMonthly,
			Status:      1,
			SortOrder:   4,
		},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			log.Printf("创建商品失败: %v", err)
			return err
		}
	}

	log.Println("默认商品创建完成")
	return nil
}

// initDefaultAdmin 初始化默认管理员
func initDefaultAdmin(db *gorm.DB) error {
	// 检查是否已有管理员数据
	var count int64
	db.Model(&Admin{}).Count(&count)
	if count > 0 {
		log.Println("管理员数据已存在，跳过初始化")
		return nil
	}

	// 创建默认管理员账号
	// 密码: admin123 (实际使用时应该使用bcrypt加密)
	admin := Admin{
		Username: "admin",
		Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // admin123
		Name:     "系统管理员",
		Email:    "<EMAIL>",
		Role:     "super_admin",
		Status:   1,
	}

	if err := db.Create(&admin).Error; err != nil {
		log.Printf("创建默认管理员失败: %v", err)
		return err
	}

	log.Println("默认管理员创建完成")
	return nil
}

// DropAllTables 删除所有表（谨慎使用）
func DropAllTables(db *gorm.DB) error {
	log.Println("警告：正在删除所有数据库表...")

	// 按依赖关系逆序删除表
	tables := []interface{}{
		&InterviewLog{},
		&Order{},
		&SMSCode{},
		&User{},
		&Product{},
		&Admin{},
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			log.Printf("删除表失败: %v", err)
			return err
		}
	}

	log.Println("所有表删除完成")
	return nil
}

// ResetDatabase 重置数据库（删除所有表并重新创建）
func ResetDatabase(db *gorm.DB) error {
	log.Println("开始重置数据库...")

	// 删除所有表
	if err := DropAllTables(db); err != nil {
		return err
	}

	// 重新创建表
	if err := AutoMigrate(db); err != nil {
		return err
	}

	// 初始化默认数据
	if err := InitDefaultData(db); err != nil {
		return err
	}

	log.Println("数据库重置完成")
	return nil
}
