package service

import (
	"fmt"
	"log"
	"time"

	"interviewmaster/internal/config"
	"interviewmaster/internal/model"
	"interviewmaster/pkg/audio"
	"interviewmaster/pkg/gemini"
	"interviewmaster/pkg/websocket"
)

// GeminiService Gemini服务
type GeminiService struct {
	client           *gemini.Client
	audioProcessor   *audio.AudioProcessor
	interviewService *InterviewService
}

// NewGeminiService 创建Gemini服务实例
func NewGeminiService() *GeminiService {
	cfg := config.GlobalConfig
	if cfg == nil {
		log.Fatal("配置未初始化")
	}

	return &GeminiService{
		client:           gemini.NewClient(&cfg.Gemini),
		audioProcessor:   audio.NewAudioProcessor(&cfg.Audio),
		interviewService: NewInterviewService(),
	}
}

// StartInterviewSession 开始面试会话
func (s *GeminiService) StartInterviewSession(userID uint64, domain string) (string, error) {
	// 获取用户信息确定A/B测试分组
	userService := NewUserService()
	user, err := userService.GetUserByID(userID)
	if err != nil {
		return "", fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return "", fmt.Errorf("用户不存在")
	}

	// 确定提示词版本
	promptVersion := user.ABTestGroup
	if promptVersion == "" {
		promptVersion = "A"
	}

	// 创建Gemini会话
	session, err := s.client.CreateSession(userID, domain, promptVersion)
	if err != nil {
		return "", fmt.Errorf("创建Gemini会话失败: %v", err)
	}

	// 发送会话开始消息到WebSocket
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:      "session_start",
		SessionID: session.ID,
		Data: map[string]interface{}{
			"session_id": session.ID,
			"domain":     domain,
			"version":    promptVersion,
		},
	})

	log.Printf("用户 %d 开始面试会话: %s, 领域: %s, 版本: %s",
		userID, session.ID, domain, promptVersion)

	return session.ID, nil
}

// EndInterviewSession 结束面试会话
func (s *GeminiService) EndInterviewSession(sessionID string) error {
	session := s.client.GetSession(sessionID)
	if session == nil {
		return fmt.Errorf("会话不存在: %s", sessionID)
	}

	userID := session.UserID

	// 关闭Gemini会话
	s.client.CloseSession(sessionID)

	// 发送会话结束消息到WebSocket
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:      "session_end",
		SessionID: sessionID,
	})

	log.Printf("用户 %d 结束面试会话: %s", userID, sessionID)
	return nil
}

// ProcessAudioInput 处理音频输入
func (s *GeminiService) ProcessAudioInput(userID uint64, sessionID string, audioData []byte, format string) error {
	startTime := time.Now()

	// 获取会话
	session := s.client.GetSession(sessionID)
	if session == nil {
		return fmt.Errorf("会话不存在: %s", sessionID)
	}

	// 发送状态更新：正在处理
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "processing",
	})

	// 处理音频数据
	processedAudio, err := s.audioProcessor.ConvertAudio(audioData, format)
	if err != nil {
		log.Printf("音频处理失败: %v", err)
		websocket.SendMessageToUser(userID, websocket.Message{
			Type:   "status_update",
			Status: "error",
			Data:   map[string]interface{}{"error": "音频处理失败"},
		})
		return err
	}

	// 发送状态更新：AI思考中
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "thinking",
	})

	// 构建音频数据
	geminiAudioData := &gemini.AudioData{
		Format:     processedAudio.Format.Format,
		SampleRate: processedAudio.Format.SampleRate,
		Channels:   processedAudio.Format.Channels,
		Data:       processedAudio.Data,
	}

	// 发送到Gemini API
	response, err := s.client.SendAudio(sessionID, geminiAudioData)
	if err != nil {
		log.Printf("Gemini API调用失败: %v", err)
		websocket.SendMessageToUser(userID, websocket.Message{
			Type:   "status_update",
			Status: "error",
			Data:   map[string]interface{}{"error": "AI服务暂时不可用"},
		})
		return err
	}

	// 计算响应时间
	responseTime := time.Since(startTime).Milliseconds()

	// 提取回答文本
	var answerText string
	if len(response.Content) > 0 && response.Content[0].Type == "text" {
		if text, ok := response.Content[0].Data.(string); ok {
			answerText = text
		}
	}

	// 记录面试日志
	interviewLog := &model.InterviewLog{
		UserID:         userID,
		SessionID:      sessionID,
		PromptVersion:  session.Config.PromptVersion,
		QuestionText:   "用户发送了音频问题", // 这里可以集成语音识别
		AnswerText:     answerText,
		ResponseTimeMs: int(responseTime),
	}

	if err := s.interviewService.CreateInterviewLog(interviewLog); err != nil {
		log.Printf("保存面试日志失败: %v", err)
	}

	// 发送状态更新：正在播报
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "speaking",
	})

	// 这里应该将文本转换为语音，然后发送音频数据
	// 为简化示例，直接发送文本
	websocket.SendMessageToUser(userID, websocket.Message{
		Type: "audio_output",
		Data: map[string]interface{}{
			"text":          answerText,
			"format":        "text", // 实际应该是音频格式
			"session_id":    sessionID,
			"response_time": responseTime,
		},
	})

	// 发送状态更新：空闲
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "idle",
	})

	log.Printf("处理用户 %d 音频输入完成，响应时间: %dms", userID, responseTime)
	return nil
}

// ProcessTextInput 处理文本输入
func (s *GeminiService) ProcessTextInput(userID uint64, sessionID string, text string) error {
	startTime := time.Now()

	// 获取会话
	session := s.client.GetSession(sessionID)
	if session == nil {
		return fmt.Errorf("会话不存在: %s", sessionID)
	}

	// 发送状态更新：AI思考中
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "thinking",
	})

	// 发送到Gemini API
	response, err := s.client.SendText(sessionID, text)
	if err != nil {
		log.Printf("Gemini API调用失败: %v", err)
		websocket.SendMessageToUser(userID, websocket.Message{
			Type:   "status_update",
			Status: "error",
			Data:   map[string]interface{}{"error": "AI服务暂时不可用"},
		})
		return err
	}

	// 计算响应时间
	responseTime := time.Since(startTime).Milliseconds()

	// 提取回答文本
	var answerText string
	if len(response.Content) > 0 && response.Content[0].Type == "text" {
		if responseText, ok := response.Content[0].Data.(string); ok {
			answerText = responseText
		}
	}

	// 记录面试日志
	interviewLog := &model.InterviewLog{
		UserID:         userID,
		SessionID:      sessionID,
		PromptVersion:  session.Config.PromptVersion,
		QuestionText:   text,
		AnswerText:     answerText,
		ResponseTimeMs: int(responseTime),
	}

	if err := s.interviewService.CreateInterviewLog(interviewLog); err != nil {
		log.Printf("保存面试日志失败: %v", err)
	}

	// 发送回答
	websocket.SendMessageToUser(userID, websocket.Message{
		Type: "text_output",
		Data: map[string]interface{}{
			"text":          answerText,
			"session_id":    sessionID,
			"response_time": responseTime,
		},
	})

	// 发送状态更新：空闲
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "idle",
	})

	log.Printf("处理用户 %d 文本输入完成，响应时间: %dms", userID, responseTime)
	return nil
}

// InterruptSession 中断会话
func (s *GeminiService) InterruptSession(userID uint64, sessionID string) error {
	// 发送中断状态
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "interrupted",
	})

	// 这里应该中断正在进行的Gemini API调用
	// 为简化示例，直接发送空闲状态
	websocket.SendMessageToUser(userID, websocket.Message{
		Type:   "status_update",
		Status: "idle",
	})

	log.Printf("用户 %d 中断会话: %s", userID, sessionID)
	return nil
}

// GetSessionInfo 获取会话信息
func (s *GeminiService) GetSessionInfo(sessionID string) (*gemini.Session, error) {
	session := s.client.GetSession(sessionID)
	if session == nil {
		return nil, fmt.Errorf("会话不存在: %s", sessionID)
	}
	return session, nil
}

// GetSessionStats 获取会话统计
func (s *GeminiService) GetSessionStats() map[string]interface{} {
	return s.client.GetSessionStats()
}
