package service

import (
	"errors"
	"time"

	"gorm.io/gorm"

	"interviewmaster/internal/model"
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/database"
)

// InterviewService 面试服务
type InterviewService struct {
	db *gorm.DB
}

// NewInterviewService 创建面试服务实例
func NewInterviewService() *InterviewService {
	return &InterviewService{
		db: database.GetDB(),
	}
}

// CreateInterviewLog 创建面试日志
func (s *InterviewService) CreateInterviewLog(log *model.InterviewLog) error {
	return s.db.Create(log).Error
}

// GetUserInterviewHistory 获取用户面试历史
func (s *InterviewService) GetUserInterviewHistory(userID uint64, page, pageSize int) ([]model.InterviewLog, int64, error) {
	var logs []model.InterviewLog
	var total int64

	offset := utils.GetOffset(page, pageSize)

	// 获取总数
	if err := s.db.Model(&model.InterviewLog{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取面试历史列表
	err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&logs).Error

	return logs, total, err
}

// GetInterviewLogByID 根据ID获取面试日志
func (s *InterviewService) GetInterviewLogByID(id uint64) (*model.InterviewLog, error) {
	var log model.InterviewLog
	err := s.db.Where("id = ?", id).First(&log).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// UpdateInterviewLogFeedback 更新面试日志反馈
func (s *InterviewService) UpdateInterviewLogFeedback(id uint64, userID uint64, feedback int8) error {
	// 验证反馈值
	if feedback < -1 || feedback > 1 {
		return errors.New("无效的反馈值")
	}

	return s.db.Model(&model.InterviewLog{}).
		Where("id = ? AND user_id = ?", id, userID).
		Update("user_feedback", feedback).Error
}

// GetInterviewLogsBySession 根据会话ID获取面试日志
func (s *InterviewService) GetInterviewLogsBySession(sessionID string) ([]model.InterviewLog, error) {
	var logs []model.InterviewLog
	err := s.db.Where("session_id = ?", sessionID).
		Order("created_at ASC").
		Find(&logs).Error
	return logs, err
}

// GetRecentInterviewLogs 获取最近的面试日志（用于历史记录展示）
func (s *InterviewService) GetRecentInterviewLogs(userID uint64, limit int) ([]model.InterviewLog, error) {
	var logs []model.InterviewLog
	err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	return logs, err
}

// GetInterviewStats 获取面试统计数据
func (s *InterviewService) GetInterviewStats(userID uint64) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总面试次数
	var totalCount int64
	if err := s.db.Model(&model.InterviewLog{}).Where("user_id = ?", userID).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total_count"] = totalCount

	// 本月面试次数
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var monthCount int64
	if err := s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND created_at >= ?", userID, monthStart).
		Count(&monthCount).Error; err != nil {
		return nil, err
	}
	stats["month_count"] = monthCount

	// 今日面试次数
	dayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	var todayCount int64
	if err := s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND created_at >= ?", userID, dayStart).
		Count(&todayCount).Error; err != nil {
		return nil, err
	}
	stats["today_count"] = todayCount

	// 平均响应时间
	var avgResponseTime float64
	if err := s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND response_time_ms > 0", userID).
		Select("AVG(response_time_ms)").
		Scan(&avgResponseTime).Error; err != nil {
		return nil, err
	}
	stats["avg_response_time"] = avgResponseTime

	// 用户反馈统计
	var positiveCount, negativeCount int64
	s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND user_feedback = 1", userID).
		Count(&positiveCount)
	s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND user_feedback = -1", userID).
		Count(&negativeCount)
	
	stats["positive_feedback"] = positiveCount
	stats["negative_feedback"] = negativeCount

	return stats, nil
}

// DeleteInterviewLog 删除面试日志
func (s *InterviewService) DeleteInterviewLog(id uint64, userID uint64) error {
	return s.db.Where("id = ? AND user_id = ?", id, userID).Delete(&model.InterviewLog{}).Error
}

// CleanOldInterviewLogs 清理旧的面试日志（保留最近N天的数据）
func (s *InterviewService) CleanOldInterviewLogs(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return s.db.Where("created_at < ?", cutoffTime).Delete(&model.InterviewLog{}).Error
}

// GetInterviewLogsByDateRange 根据日期范围获取面试日志
func (s *InterviewService) GetInterviewLogsByDateRange(userID uint64, startDate, endDate time.Time, page, pageSize int) ([]model.InterviewLog, int64, error) {
	var logs []model.InterviewLog
	var total int64

	offset := utils.GetOffset(page, pageSize)

	query := s.db.Model(&model.InterviewLog{}).Where("user_id = ? AND created_at >= ? AND created_at <= ?", userID, startDate, endDate)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取日志列表
	err := query.Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&logs).Error

	return logs, total, err
}
