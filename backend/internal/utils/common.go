package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// GenerateOrderNo 生成订单号
func GenerateOrderNo() string {
	now := time.Now()
	timestamp := now.Format("20060102150405")
	
	// 生成6位随机数
	randomNum, _ := rand.Int(rand.Reader, big.NewInt(1000000))
	
	return fmt.Sprintf("IM%s%06d", timestamp, randomNum.Int64())
}

// GenerateSMSCode 生成短信验证码
func GenerateSMSCode() string {
	code, _ := rand.Int(rand.Reader, big.NewInt(1000000))
	return fmt.Sprintf("%06d", code.Int64())
}

// GenerateSessionID 生成会话ID
func GenerateSessionID() string {
	now := time.Now()
	timestamp := now.UnixNano()
	
	// 生成8位随机数
	randomNum, _ := rand.Int(rand.Reader, big.NewInt(100000000))
	
	return fmt.Sprintf("session_%d_%08d", timestamp, randomNum.Int64())
}

// IsValidPhone 验证手机号格式
func IsValidPhone(phone string) bool {
	// 中国大陆手机号正则表达式
	pattern := `^1[3-9]\d{9}$`
	matched, _ := regexp.MatchString(pattern, phone)
	return matched
}

// IsValidSMSCode 验证短信验证码格式
func IsValidSMSCode(code string) bool {
	// 6位数字
	pattern := `^\d{6}$`
	matched, _ := regexp.MatchString(pattern, code)
	return matched
}

// StringToUint64 字符串转uint64
func StringToUint64(s string) (uint64, error) {
	return strconv.ParseUint(s, 10, 64)
}

// StringToInt 字符串转int
func StringToInt(s string) (int, error) {
	return strconv.Atoi(s)
}

// StringToFloat64 字符串转float64
func StringToFloat64(s string) (float64, error) {
	return strconv.ParseFloat(s, 64)
}

// GetPageParams 获取分页参数
func GetPageParams(pageStr, pageSizeStr string) (page, pageSize int) {
	page = 1
	pageSize = 20

	if pageStr != "" {
		if p, err := StringToInt(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr != "" {
		if ps, err := StringToInt(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	return
}

// GetOffset 计算分页偏移量
func GetOffset(page, pageSize int) int {
	return (page - 1) * pageSize
}

// InSlice 检查元素是否在切片中
func InSlice(item string, slice []string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// RemoveFromSlice 从切片中移除元素
func RemoveFromSlice(item string, slice []string) []string {
	result := make([]string, 0)
	for _, s := range slice {
		if s != item {
			result = append(result, s)
		}
	}
	return result
}

// TrimSpaces 去除字符串两端空格
func TrimSpaces(s string) string {
	return strings.TrimSpace(s)
}

// IsEmpty 检查字符串是否为空
func IsEmpty(s string) bool {
	return TrimSpaces(s) == ""
}

// DefaultString 如果字符串为空则返回默认值
func DefaultString(s, defaultValue string) string {
	if IsEmpty(s) {
		return defaultValue
	}
	return s
}

// FormatDuration 格式化时长（秒转为可读格式）
func FormatDuration(seconds uint32) string {
	if seconds < 60 {
		return fmt.Sprintf("%d秒", seconds)
	}
	
	minutes := seconds / 60
	if minutes < 60 {
		return fmt.Sprintf("%d分钟", minutes)
	}
	
	hours := minutes / 60
	if hours < 24 {
		return fmt.Sprintf("%d小时", hours)
	}
	
	days := hours / 24
	return fmt.Sprintf("%d天", days)
}

// GetABTestGroup 根据用户ID获取A/B测试分组
func GetABTestGroup(userID uint64, groups []string) string {
	if len(groups) == 0 {
		return "A"
	}
	
	// 使用用户ID的哈希值来分组，确保同一用户总是分到同一组
	groupIndex := userID % uint64(len(groups))
	return groups[groupIndex]
}

// MaskPhone 手机号脱敏
func MaskPhone(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// MaskEmail 邮箱脱敏
func MaskEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}
	
	username := parts[0]
	domain := parts[1]
	
	if len(username) <= 2 {
		return email
	}
	
	maskedUsername := username[:1] + strings.Repeat("*", len(username)-2) + username[len(username)-1:]
	return maskedUsername + "@" + domain
}
