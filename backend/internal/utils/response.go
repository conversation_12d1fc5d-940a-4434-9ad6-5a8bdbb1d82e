package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// SuccessResponse 成功响应
func SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// SuccessResponseWithMessage 带消息的成功响应
func SuccessResponseWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: message,
		Data:    data,
	})
}

// ErrorResponse 错误响应
func ErrorResponse(c *gin.Context, httpCode int, message string) {
	c.JSON(httpCode, Response{
		Code:    httpCode,
		Message: message,
	})
}

// ErrorResponseWithCode 带自定义错误码的错误响应
func ErrorResponseWithCode(c *gin.Context, httpCode int, code int, message string) {
	c.J<PERSON>(httpCode, Response{
		Code:    code,
		Message: message,
	})
}

// ValidationErrorResponse 参数验证错误响应
func ValidationErrorResponse(c *gin.Context, message string) {
	ErrorResponse(c, http.StatusBadRequest, message)
}

// UnauthorizedResponse 未授权响应
func UnauthorizedResponse(c *gin.Context, message string) {
	if message == "" {
		message = "未授权访问"
	}
	ErrorResponse(c, http.StatusUnauthorized, message)
}

// ForbiddenResponse 禁止访问响应
func ForbiddenResponse(c *gin.Context, message string) {
	if message == "" {
		message = "禁止访问"
	}
	ErrorResponse(c, http.StatusForbidden, message)
}

// NotFoundResponse 资源不存在响应
func NotFoundResponse(c *gin.Context, message string) {
	if message == "" {
		message = "资源不存在"
	}
	ErrorResponse(c, http.StatusNotFound, message)
}

// InternalErrorResponse 内部服务器错误响应
func InternalErrorResponse(c *gin.Context, message string) {
	if message == "" {
		message = "内部服务器错误"
	}
	ErrorResponse(c, http.StatusInternalServerError, message)
}

// TooManyRequestsResponse 请求过多响应
func TooManyRequestsResponse(c *gin.Context, message string) {
	if message == "" {
		message = "请求过于频繁"
	}
	ErrorResponse(c, http.StatusTooManyRequests, message)
}

// PageResponse 分页响应结构
type PageResponse struct {
	List       interface{} `json:"list"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// PageSuccessResponse 分页成功响应
func PageSuccessResponse(c *gin.Context, list interface{}, total int64, page, pageSize int) {
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	SuccessResponse(c, PageResponse{
		List:       list,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	})
}
