package sms

import (
	"encoding/json"
	"errors"
	"fmt"

	"log"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
)

// AliyunSMSConfig 阿里云短信配置
type AliyunSMSConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	SignName        string `json:"sign_name"`
	TemplateCode    string `json:"template_code"`
	Region          string `json:"region"`
	Test            bool   `json:"test"`     // Added for testing
	TestCode        string `json:"testCode"` // Added for testing
}

// AliyunSMSClient 阿里云短信客户端
type AliyunSMSClient struct {
	client       *dysmsapi.Client
	signName     string
	templateCode string
	testMode     bool
	testCode     string
}

// TestMode 返回客户端是否处于测试模式
func (c *AliyunSMSClient) TestMode() bool {
	return c.testMode
}

// GetTestCode 返回测试模式下的验证码
func (c *AliyunSMSClient) GetTestCode() string {
	return c.testCode
}

// NewAliyunSMSClient 创建阿里云短信客户端
func NewAliyunSMSClient(config AliyunSMSConfig) (*AliyunSMSClient, error) {
	if config.Test {
		log.Printf("INFO: 阿里云短信客户端已开启测试模式，测试验证码: %s", config.TestCode)
		return &AliyunSMSClient{
			testMode: true,
			testCode: config.TestCode,
		}, nil
	}

	client, err := dysmsapi.NewClientWithAccessKey(
		config.Region,
		config.AccessKeyID,
		config.AccessKeySecret,
	)
	if err != nil {
		return nil, err
	}

	return &AliyunSMSClient{
		client:       client,
		signName:     config.SignName,
		templateCode: config.TemplateCode,
		testMode:     false,
	}, nil
}

// SendVerificationCode 发送验证码
func (c *AliyunSMSClient) SendVerificationCode(phone, code string) error {
	if c.testMode {
		log.Printf("INFO: 短信测试模式：已向手机号 %s 发送验证码 %s (实际发送: %s)", phone, c.testCode, c.testCode)
		return nil
	}

	request := dysmsapi.CreateSendSmsRequest()
	request.Scheme = "https"
	request.PhoneNumbers = phone
	request.SignName = c.signName
	request.TemplateCode = c.templateCode

	// 构建模板参数
	templateParam := map[string]string{
		"code": code,
	}
	paramJSON, err := json.Marshal(templateParam)
	if err != nil {
		return err
	}
	request.TemplateParam = string(paramJSON)

	// 发送短信
	response, err := c.client.SendSms(request)
	if err != nil {
		log.Printf("ERROR: 发送短信失败: %v", err)
		return err
	}

	// 检查发送结果
	if response.Code != "OK" {
		log.Printf("ERROR: 短信发送失败: %s, %s", response.Code, response.Message)
		return errors.New(fmt.Sprintf("短信发送失败: %s", response.Message))
	}

	log.Printf("INFO: 短信发送成功，手机号: %s, 验证码: %s", phone, code)
	return nil
}
