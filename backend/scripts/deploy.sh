#!/bin/bash

# 面试助手后端部署脚本
# 使用方法: ./scripts/deploy.sh [环境] [版本]
# 示例: ./scripts/deploy.sh production v1.0.0

set -e

# 默认参数
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}
APP_NAME="interview-master"
BUILD_DIR="build"
LOG_DIR="logs"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装"
        exit 1
    fi
    
    if ! command -v mysql &> /dev/null; then
        log_warn "MySQL 客户端未安装，跳过数据库检查"
    fi
    
    if ! command -v redis-cli &> /dev/null; then
        log_warn "Redis 客户端未安装，跳过Redis检查"
    fi
    
    log_info "依赖检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建目录..."
    
    mkdir -p $BUILD_DIR
    mkdir -p $LOG_DIR
    mkdir -p data/uploads
    
    log_info "目录创建完成"
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    # 设置构建信息
    BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    # 构建标志
    LDFLAGS="-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT"
    
    # 根据环境设置构建参数
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "生产环境构建..."
        CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
            -ldflags "$LDFLAGS" \
            -o $BUILD_DIR/$APP_NAME \
            ./
    else
        log_info "开发环境构建..."
        go build \
            -ldflags "$LDFLAGS" \
            -o $BUILD_DIR/$APP_NAME \
            ./
    fi
    
    log_info "构建完成: $BUILD_DIR/$APP_NAME"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 单元测试
    go test -v ./... -cover
    
    # 集成测试 (如果存在)
    if [ -d "tests" ]; then
        go test -v ./tests/...
    fi
    
    log_info "测试完成"
}

# 数据库迁移
migrate_database() {
    log_info "数据库迁移..."
    
    if [ -f "config.json" ]; then
        # 构建迁移工具
        go build -o $BUILD_DIR/migrate ./cmd/migrate
        
        # 运行迁移
        ./$BUILD_DIR/migrate -action=migrate
        
        log_info "数据库迁移完成"
    else
        log_warn "配置文件不存在，跳过数据库迁移"
    fi
}

# 停止旧服务
stop_old_service() {
    log_info "停止旧服务..."
    
    # 查找并停止旧进程
    if pgrep -f $APP_NAME > /dev/null; then
        log_info "发现运行中的服务，正在停止..."
        pkill -f $APP_NAME
        sleep 2
        
        # 强制停止
        if pgrep -f $APP_NAME > /dev/null; then
            log_warn "强制停止服务..."
            pkill -9 -f $APP_NAME
        fi
    fi
    
    log_info "旧服务已停止"
}

# 启动新服务
start_service() {
    log_info "启动新服务..."
    
    # 检查配置文件
    if [ ! -f "config.json" ]; then
        if [ -f "config.example.json" ]; then
            log_warn "配置文件不存在，复制示例配置"
            cp config.example.json config.json
            log_error "请编辑 config.json 文件并重新运行部署"
            exit 1
        else
            log_error "配置文件和示例配置都不存在"
            exit 1
        fi
    fi
    
    # 启动服务
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "生产环境启动..."
        nohup ./$BUILD_DIR/$APP_NAME > $LOG_DIR/app.log 2>&1 &
        echo $! > $BUILD_DIR/$APP_NAME.pid
    else
        log_info "开发环境启动..."
        ./$BUILD_DIR/$APP_NAME &
        echo $! > $BUILD_DIR/$APP_NAME.pid
    fi
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if pgrep -f $APP_NAME > /dev/null; then
        log_info "服务启动成功 (PID: $(cat $BUILD_DIR/$APP_NAME.pid))"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "健康检查..."
    
    # 等待服务完全启动
    sleep 5
    
    # 检查健康接口
    if command -v curl &> /dev/null; then
        for i in {1..10}; do
            if curl -f http://localhost:8080/health > /dev/null 2>&1; then
                log_info "健康检查通过"
                return 0
            fi
            log_info "等待服务启动... ($i/10)"
            sleep 2
        done
        log_error "健康检查失败"
        return 1
    else
        log_warn "curl 未安装，跳过健康检查"
    fi
}

# 备份当前版本
backup_current() {
    if [ -f "$BUILD_DIR/$APP_NAME" ]; then
        log_info "备份当前版本..."
        cp $BUILD_DIR/$APP_NAME $BUILD_DIR/$APP_NAME.backup.$(date +%Y%m%d_%H%M%S)
        log_info "备份完成"
    fi
}

# 回滚
rollback() {
    log_error "部署失败，开始回滚..."
    
    # 停止当前服务
    stop_old_service
    
    # 恢复备份
    BACKUP_FILE=$(ls -t $BUILD_DIR/$APP_NAME.backup.* 2>/dev/null | head -1)
    if [ -n "$BACKUP_FILE" ]; then
        log_info "恢复备份: $BACKUP_FILE"
        cp $BACKUP_FILE $BUILD_DIR/$APP_NAME
        start_service
    else
        log_error "没有找到备份文件"
    fi
}

# 清理
cleanup() {
    log_info "清理临时文件..."
    
    # 保留最近3个备份
    ls -t $BUILD_DIR/$APP_NAME.backup.* 2>/dev/null | tail -n +4 | xargs rm -f
    
    log_info "清理完成"
}

# 显示帮助
show_help() {
    echo "面试助手后端部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [版本]"
    echo ""
    echo "参数:"
    echo "  环境    部署环境 (development|production) [默认: development]"
    echo "  版本    应用版本 [默认: latest]"
    echo ""
    echo "示例:"
    echo "  $0 development v1.0.0"
    echo "  $0 production v1.0.0"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --no-test      跳过测试"
    echo "  --no-migrate   跳过数据库迁移"
}

# 主函数
main() {
    log_info "开始部署 $APP_NAME ($ENVIRONMENT 环境, 版本: $VERSION)"
    
    # 解析参数
    SKIP_TEST=false
    SKIP_MIGRATE=false
    
    for arg in "$@"; do
        case $arg in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-test)
                SKIP_TEST=true
                ;;
            --no-migrate)
                SKIP_MIGRATE=true
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    create_directories
    
    if [ "$SKIP_TEST" = false ]; then
        run_tests
    fi
    
    backup_current
    build_app
    
    if [ "$SKIP_MIGRATE" = false ]; then
        migrate_database
    fi
    
    stop_old_service
    start_service
    
    if health_check; then
        cleanup
        log_info "部署成功完成！"
        log_info "应用版本: $VERSION"
        log_info "PID文件: $BUILD_DIR/$APP_NAME.pid"
        log_info "日志文件: $LOG_DIR/app.log"
    else
        rollback
        exit 1
    fi
}

# 错误处理
trap 'log_error "部署过程中发生错误"; rollback; exit 1' ERR

# 运行主函数
main "$@"
