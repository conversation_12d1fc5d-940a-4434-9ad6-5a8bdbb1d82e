-- 创建数据库
CREATE DATABASE IF NOT EXISTS interview_master DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE interview_master;

-- 用户表 (users)
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
  `nickname` VARCHAR(50) DEFAULT '' COMMENT '昵称',
  `balance_count` INT UNSIGNED DEFAULT 0 COMMENT '剩余次数',
  `balance_duration` INT UNSIGNED DEFAULT 0 COMMENT '剩余时长(秒)',
  `free_trial_count` TINYINT UNSIGNED DEFAULT 3 COMMENT '剩余免费试用次数',
  `ab_test_group` VARCHAR(10) COMMENT 'A/B测试分组',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 订单表 (orders)
CREATE TABLE `orders` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_no` VARCHAR(64) NOT NULL UNIQUE COMMENT '订单号',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `product_id` VARCHAR(50) NOT NULL COMMENT '商品ID',
  `amount` DECIMAL(10, 2) NOT NULL COMMENT '订单金额',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '订单状态 0:待支付 1:已支付 2:已关闭',
  `pay_type` VARCHAR(20) COMMENT '支付方式 wechat/alipay',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `paid_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_order_no` (`order_no`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 面试日志表 (interview_logs)
CREATE TABLE `interview_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `session_id` VARCHAR(128) NOT NULL COMMENT '会话ID',
  `prompt_version` VARCHAR(20) COMMENT '使用的提示词版本',
  `question_text` TEXT COMMENT '识别出的问题文本',
  `answer_text` TEXT COMMENT '生成的回答文本',
  `response_time_ms` INT COMMENT '从问题结束到回答开始的延迟(ms)',
  `user_feedback` TINYINT DEFAULT 0 COMMENT '用户反馈 0:无 1:赞 -1:踩',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='面试日志表';

-- 短信验证码表 (sms_codes)
CREATE TABLE `sms_codes` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `code` VARCHAR(10) NOT NULL COMMENT '验证码',
  `type` TINYINT NOT NULL DEFAULT 1 COMMENT '类型 1:注册 2:登录 3:重置密码',
  `used` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已使用 0:未使用 1:已使用',
  `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_phone_code` (`phone`, `code`),
  INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信验证码表';

-- 商品表 (products)
CREATE TABLE `products` (
  `id` VARCHAR(50) NOT NULL COMMENT '商品ID',
  `name` VARCHAR(100) NOT NULL COMMENT '商品名称',
  `description` TEXT COMMENT '商品描述',
  `price` DECIMAL(10, 2) NOT NULL COMMENT '价格',
  `count` INT UNSIGNED DEFAULT 0 COMMENT '包含次数',
  `duration` INT UNSIGNED DEFAULT 0 COMMENT '包含时长(秒)',
  `type` TINYINT NOT NULL DEFAULT 1 COMMENT '类型 1:次数包 2:时长包 3:包月',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:下架 1:上架',
  `sort_order` INT DEFAULT 0 COMMENT '排序',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_status_sort` (`status`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 插入默认商品数据
INSERT INTO `products` (`id`, `name`, `description`, `price`, `count`, `duration`, `type`, `status`, `sort_order`) VALUES
('trial_10', '新手体验包', '10次面试机会，适合初次体验', 9.90, 10, 0, 1, 1, 1),
('standard_50', '标准套餐', '50次面试机会，适合求职准备', 39.90, 50, 0, 1, 1, 2),
('premium_100', '高级套餐', '100次面试机会，适合长期使用', 69.90, 100, 0, 1, 1, 3),
('monthly_unlimited', '包月畅享', '一个月内无限次使用', 99.90, 0, 2592000, 3, 1, 4);

-- 管理员表 (admins)
CREATE TABLE `admins` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码hash',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `email` VARCHAR(100) COMMENT '邮箱',
  `role` VARCHAR(20) DEFAULT 'admin' COMMENT '角色',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 插入默认管理员账号 (密码: admin123)
INSERT INTO `admins` (`username`, `password`, `name`, `email`, `role`) VALUES
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', 'super_admin');
