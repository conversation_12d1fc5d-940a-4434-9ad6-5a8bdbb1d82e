version: '3.8'

services:
  # 后端应用
  app:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: interview-master-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
    volumes:
      - ./backend/config.json:/app/config.json:ro
      - ./backend/logs:/app/logs
      - ./backend/data:/app/data
    depends_on:
      - mysql
      - redis
    networks:
      - interview-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: interview-master-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-interview123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-interview_master}
      MYSQL_USER: ${MYSQL_USER:-interview}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-interview123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro
    ports:
      - "3306:3306"
    networks:
      - interview-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: interview-master-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - interview-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-interview123}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: interview-master-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./app/dist:/usr/share/nginx/html:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - interview-network

  # 前端构建 (可选)
  frontend-build:
    image: node:18-alpine
    container_name: interview-master-frontend-build
    working_dir: /app
    volumes:
      - ./app:/app
    command: sh -c "npm install && npm run build:h5"
    profiles:
      - build

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  interview-network:
    driver: bridge
